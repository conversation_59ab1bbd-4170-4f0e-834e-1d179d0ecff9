// 2D Tiles World Map System
class WorldMap {
    constructor() {
        this.currentLocation = 'spawn';
        this.mapWidth = window.innerWidth <= 768 ? 8 : 12;
        this.mapHeight = window.innerWidth <= 768 ? 6 : 8;
        
        // Define world locations with their properties
        this.locations = {
            spawn: {
                name: '🏝️ Spawn Island',
                description: 'Tvůj domovský ostrov. Odtud začíná tvoje dobrodružství!',
                emoji: '🏝️',
                type: 'spawn',
                unlocked: true,
                position: this.getAdaptivePosition(5, 4, 4, 3),
                actions: [
                    { name: 'Odpočinout si', emoji: '😴', action: 'rest' },
                    { name: '<PERSON>zkoumat', emoji: '🔍', action: 'explore' }
                ]
            },
            farm: {
                name: '🌾 The Barn',
                description: 'Zelené pole plné plodin. Ideální místo pro farmařství!',
                emoji: '🌾',
                type: 'farm',
                unlocked: false,
                position: this.getAdaptivePosition(3, 3, 2, 2),
                requirements: { skill: 'FARMING', level: 1 },
                actions: [
                    { name: 'Farmařit', emoji: '🌾', action: 'farm', skill: 'FARMING' },
                    { name: 'Pěstovat plodiny', emoji: '🌱', action: 'plant' },
                    { name: 'Sklízet', emoji: '🚜', action: 'harvest' }
                ]
            },
            mine: {
                name: '⛏️ Gold Mine',
                description: 'Temné tunely plné drahocenných rud a nerostů.',
                emoji: '⛏️',
                type: 'mine',
                unlocked: false,
                position: this.getAdaptivePosition(8, 2, 6, 1),
                requirements: { skill: 'MINING', level: 1 },
                actions: [
                    { name: 'Těžit', emoji: '⛏️', action: 'mine', skill: 'MINING' },
                    { name: 'Hledat rudy', emoji: '💎', action: 'prospect' },
                    { name: 'Kopat tunely', emoji: '🕳️', action: 'dig' }
                ]
            },
            forest: {
                name: '🌲 Dark Forest',
                description: 'Hustý les plný vzácného dřeva a tajemných tvorů.',
                emoji: '🌲',
                type: 'forest',
                unlocked: false,
                position: this.getAdaptivePosition(2, 5, 1, 4),
                requirements: { skill: 'FORAGING', level: 1 },
                actions: [
                    { name: 'Kácet stromy', emoji: '🪓', action: 'chop', skill: 'FORAGING' },
                    { name: 'Sbírat dřevo', emoji: '🪵', action: 'gather' },
                    { name: 'Prozkoumat', emoji: '🔍', action: 'explore' }
                ]
            },
            ocean: {
                name: '🌊 Fishing Pond',
                description: 'Klidné vody plné ryb a mořských pokladů.',
                emoji: '🎣',
                type: 'ocean',
                unlocked: false,
                position: this.getAdaptivePosition(9, 6, 6, 4),
                requirements: { skill: 'FISHING', level: 1 },
                actions: [
                    { name: 'Rybařit', emoji: '🎣', action: 'fish', skill: 'FISHING' },
                    { name: 'Hledat poklady', emoji: '🏴‍☠️', action: 'treasure' },
                    { name: 'Plavat', emoji: '🏊', action: 'swim' }
                ]
            },
            combat: {
                name: '⚔️ Spider\'s Den',
                description: 'Nebezpečná oblast plná monster a výzev.',
                emoji: '🕷️',
                type: 'combat',
                unlocked: false,
                position: this.getAdaptivePosition(1, 1, 1, 1),
                requirements: { skill: 'COMBAT', level: 1 },
                actions: [
                    { name: 'Bojovat', emoji: '⚔️', action: 'fight', skill: 'COMBAT' },
                    { name: 'Lovit monstra', emoji: '👹', action: 'hunt' },
                    { name: 'Trénovat', emoji: '🥊', action: 'train' }
                ]
            },
            magic: {
                name: '✨ Enchanting Tower',
                description: 'Mystická věž plná kouzel a enchantmentů.',
                emoji: '🔮',
                type: 'magic',
                unlocked: false,
                position: this.getAdaptivePosition(10, 3, 7, 2),
                requirements: { skill: 'ENCHANTING', level: 5 },
                actions: [
                    { name: 'Očarovat', emoji: '✨', action: 'enchant', skill: 'ENCHANTING' },
                    { name: 'Studovat kouzla', emoji: '📚', action: 'study' },
                    { name: 'Vařit lektvary', emoji: '🧪', action: 'brew' }
                ]
            },
            desert: {
                name: '🏜️ Mushroom Desert',
                description: 'Vyprahlá poušť s vzácnými houbami a artefakty.',
                emoji: '🍄',
                type: 'desert',
                unlocked: false,
                position: this.getAdaptivePosition(6, 1, 4, 0),
                requirements: { skill: 'FARMING', level: 5 },
                actions: [
                    { name: 'Sbírat houby', emoji: '🍄', action: 'mushroom', skill: 'FARMING' },
                    { name: 'Hledat artefakty', emoji: '🏺', action: 'artifact' },
                    { name: 'Prozkoumat ruiny', emoji: '🏛️', action: 'ruins' }
                ]
            },
            nether: {
                name: '🔥 Crimson Isle',
                description: 'Ohnivá dimenze plná nebezpečí a vzácných materiálů.',
                emoji: '🔥',
                type: 'nether',
                unlocked: false,
                position: this.getAdaptivePosition(0, 7, 0, 5),
                requirements: { skill: 'COMBAT', level: 22 },
                actions: [
                    { name: 'Bojovat s démony', emoji: '👿', action: 'demon', skill: 'COMBAT' },
                    { name: 'Těžit Netherite', emoji: '⚫', action: 'netherite', skill: 'MINING' },
                    { name: 'Sbírat Blaze rods', emoji: '🔥', action: 'blaze' }
                ]
            },
            end: {
                name: '🌌 The End',
                description: 'Konečná dimenze, domov Ender Dragona.',
                emoji: '🐉',
                type: 'end',
                unlocked: false,
                position: this.getAdaptivePosition(11, 0, 7, 0),
                requirements: { skill: 'COMBAT', level: 12 },
                actions: [
                    { name: 'Bojovat s Endermany', emoji: '👤', action: 'enderman', skill: 'COMBAT' },
                    { name: 'Hledat End Cities', emoji: '🏙️', action: 'cities' },
                    { name: 'Výzva: Ender Dragon', emoji: '🐉', action: 'dragon' }
                ]
            }
        };

        // Generate water tiles around islands
        this.generateWaterTiles();
    }

    getAdaptivePosition(desktopX, desktopY, mobileX, mobileY) {
        return window.innerWidth <= 768 ?
            { x: mobileX, y: mobileY } :
            { x: desktopX, y: desktopY };
    }

    generateWaterTiles() {
        // Add water tiles to fill empty spaces
        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                const hasLocation = Object.values(this.locations).some(loc => 
                    loc.position.x === x && loc.position.y === y
                );
                
                if (!hasLocation) {
                    const waterKey = `water_${x}_${y}`;
                    this.locations[waterKey] = {
                        name: '🌊 Ocean',
                        description: 'Hluboké modré vody oceánu.',
                        emoji: '🌊',
                        type: 'water',
                        unlocked: true,
                        position: { x, y },
                        actions: []
                    };
                }
            }
        }
    }

    initialize() {
        this.createWorldMap();
        this.updateLocationInfo();
        this.checkUnlockedLocations();
    }

    createWorldMap() {
        const worldMap = document.getElementById('world-map');
        worldMap.innerHTML = '';

        for (let y = 0; y < this.mapHeight; y++) {
            for (let x = 0; x < this.mapWidth; x++) {
                const tile = document.createElement('div');
                tile.className = 'tile';
                
                // Find location at this position
                const location = Object.values(this.locations).find(loc => 
                    loc.position.x === x && loc.position.y === y
                );

                if (location) {
                    const locationKey = Object.keys(this.locations).find(key => 
                        this.locations[key] === location
                    );
                    
                    tile.classList.add(`tile-${location.type}`);
                    tile.classList.add(location.unlocked ? 'unlocked' : 'locked');
                    
                    if (locationKey === this.currentLocation) {
                        tile.classList.add('active');
                    }

                    tile.innerHTML = `
                        ${location.emoji}
                        <div class="tile-tooltip">${location.name}</div>
                    `;

                    tile.addEventListener('click', () => {
                        if (location.unlocked) {
                            this.selectLocation(locationKey);
                        }
                    });
                }

                worldMap.appendChild(tile);
            }
        }
    }

    selectLocation(locationKey) {
        // Remove active class from all tiles
        document.querySelectorAll('.tile').forEach(tile => {
            tile.classList.remove('active');
        });

        this.currentLocation = locationKey;
        this.updateLocationInfo();
        this.createWorldMap(); // Recreate to update active state
        
        // Add log entry
        const location = this.locations[locationKey];
        if (window.game) {
            window.game.addLogEntry(`🗺️ Přesunul ses do: ${location.name}`);
        }
    }

    updateLocationInfo() {
        const location = this.locations[this.currentLocation];
        
        document.getElementById('current-location-name').textContent = location.name;
        document.getElementById('current-location-desc').textContent = location.description;
        
        const actionsContainer = document.getElementById('location-actions');
        actionsContainer.innerHTML = '';

        location.actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'location-action-btn';
            button.innerHTML = `${action.emoji} ${action.name}`;
            
            button.addEventListener('click', () => {
                this.performLocationAction(action);
            });

            actionsContainer.appendChild(button);
        });
    }

    performLocationAction(action) {
        if (action.skill) {
            // Perform skill action
            if (window.game) {
                window.game.performAction(action.skill);
            }
        } else {
            // Perform special action
            this.performSpecialAction(action.action);
        }
    }

    performSpecialAction(actionType) {
        const location = this.locations[this.currentLocation];
        
        switch (actionType) {
            case 'rest':
                if (window.game) {
                    window.game.playerData.health = Math.min(window.game.playerData.health + 10, 200);
                    window.game.addLogEntry('😴 Odpočinul sis a obnovil 10 zdraví!');
                    window.game.updateUI();
                }
                break;
            case 'explore':
                if (window.game) {
                    const xpGain = 5 + Math.floor(Math.random() * 10);
                    window.game.playerData.skyblockXP += xpGain;
                    window.game.addLogEntry(`🔍 Prozkoumal jsi ${location.name} a získal ${xpGain} SkyBlock XP!`);
                    window.game.updateUI();
                }
                break;
            case 'plant':
                if (window.game) {
                    window.game.addLogEntry('🌱 Zasadil jsi semínka! Budou růst časem.');
                }
                break;
            case 'harvest':
                if (window.game) {
                    const coins = 10 + Math.floor(Math.random() * 20);
                    window.game.playerData.coins += coins;
                    window.game.addLogEntry(`🚜 Sklidil jsi plodiny a získal ${coins} coins!`);
                    window.game.updateUI();
                }
                break;
            default:
                if (window.game) {
                    window.game.addLogEntry(`✨ Provedl jsi akci: ${actionType}`);
                }
        }
    }

    checkUnlockedLocations() {
        if (!window.game) return;

        Object.keys(this.locations).forEach(locationKey => {
            const location = this.locations[locationKey];
            
            if (!location.unlocked && location.requirements) {
                const req = location.requirements;
                if (req.skill && req.level) {
                    const playerSkill = window.game.playerData.skills[req.skill];
                    if (playerSkill && playerSkill.level >= req.level) {
                        location.unlocked = true;
                        if (window.game) {
                            window.game.addLogEntry(`🗺️ Nová oblast odemčena: ${location.name}!`);
                        }
                    }
                }
            }
        });

        this.createWorldMap();
    }
}

// Global world map instance
window.worldMap = new WorldMap();
