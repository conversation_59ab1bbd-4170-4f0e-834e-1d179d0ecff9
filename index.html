<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkyBlock Adventure</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <h1>🏝️ SkyBlock Adventure</h1>
            <div class="player-stats">
                <div class="stat">
                    <span class="stat-icon">💰</span>
                    <span id="coins">0</span> Coins
                </div>
                <div class="stat">
                    <span class="stat-icon">❤️</span>
                    <span id="health">100</span> Health
                </div>
                <div class="stat">
                    <span class="stat-icon">⭐</span>
                    <span id="skyblock-xp">0</span> SkyBlock XP
                </div>
            </div>
        </header>

        <!-- Main Game Area -->
        <div class="game-content">
            <!-- Skills Section -->
            <section class="skills-section">
                <h2>🎯 Dovednosti</h2>
                <div class="skills-grid" id="skills-grid">
                    <!-- Skills will be dynamically generated -->
                </div>
            </section>

            <!-- Actions Section -->
            <section class="actions-section">
                <h2>⚡ Akce</h2>
                <div class="actions-grid">
                    <button class="action-btn farming" onclick="performAction('FARMING')">
                        <span class="action-icon">🌾</span>
                        <span class="action-name">Farmařit</span>
                        <span class="action-desc">Získej Farming XP</span>
                    </button>
                    <button class="action-btn mining" onclick="performAction('MINING')">
                        <span class="action-icon">⛏️</span>
                        <span class="action-name">Těžit</span>
                        <span class="action-desc">Získej Mining XP</span>
                    </button>
                    <button class="action-btn combat" onclick="performAction('COMBAT')">
                        <span class="action-icon">⚔️</span>
                        <span class="action-name">Bojovat</span>
                        <span class="action-desc">Získej Combat XP</span>
                    </button>
                    <button class="action-btn foraging" onclick="performAction('FORAGING')">
                        <span class="action-icon">🪓</span>
                        <span class="action-name">Kácet</span>
                        <span class="action-desc">Získej Foraging XP</span>
                    </button>
                    <button class="action-btn fishing" onclick="performAction('FISHING')">
                        <span class="action-icon">🎣</span>
                        <span class="action-name">Rybařit</span>
                        <span class="action-desc">Získej Fishing XP</span>
                    </button>
                    <button class="action-btn enchanting" onclick="performAction('ENCHANTING')">
                        <span class="action-icon">✨</span>
                        <span class="action-name">Očarovat</span>
                        <span class="action-desc">Získej Enchanting XP</span>
                    </button>
                </div>
            </section>

            <!-- Inventory Section -->
            <section class="inventory-section">
                <h2>🎒 Inventář</h2>
                <div class="inventory-controls">
                    <button onclick="sellAllItems()" class="sell-btn">💰 Prodat vše</button>
                    <span class="inventory-count">Předměty: <span id="inventory-count">0</span></span>
                </div>
                <div class="inventory-grid" id="inventory-grid">
                    <!-- Inventory items will be dynamically generated -->
                </div>
            </section>

            <!-- Collections Section -->
            <section class="collections-section">
                <h2>📚 Kolekce</h2>
                <div class="collections-tabs">
                    <button class="tab-btn active" onclick="showCollectionTab('FARMING')">🌾 Farming</button>
                    <button class="tab-btn" onclick="showCollectionTab('MINING')">⛏️ Mining</button>
                    <button class="tab-btn" onclick="showCollectionTab('COMBAT')">⚔️ Combat</button>
                    <button class="tab-btn" onclick="showCollectionTab('FORAGING')">🪓 Foraging</button>
                    <button class="tab-btn" onclick="showCollectionTab('FISHING')">🎣 Fishing</button>
                </div>
                <div class="collections-content" id="collections-content">
                    <!-- Collections will be dynamically generated -->
                </div>
            </section>

            <!-- Activity Log -->
            <section class="log-section">
                <h2>📜 Log aktivit</h2>
                <div class="activity-log" id="activity-log">
                    <div class="log-entry">Vítej ve SkyBlock Adventure! Začni klikáním na akce.</div>
                </div>
            </section>
        </div>

        <!-- Save/Load Controls -->
        <div class="save-controls">
            <button onclick="saveGame()" class="save-btn">💾 Uložit hru</button>
            <button onclick="loadGame()" class="load-btn">📁 Načíst hru</button>
            <button onclick="resetGame()" class="reset-btn">🔄 Reset hry</button>
        </div>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <h2>🏝️ Načítání SkyBlock Adventure...</h2>
            <div class="loading-bar">
                <div class="loading-progress" id="loading-progress"></div>
            </div>
            <p id="loading-text">Načítání herních dat...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="data-loader.js"></script>
    <script src="game.js"></script>
</body>
</html>
