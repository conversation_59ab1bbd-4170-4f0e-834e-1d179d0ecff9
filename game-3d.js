// SkyBlock 3D Adventure Game Logic
class SkyBlock3DGame {
    constructor() {
        this.playerData = {
            coins: 0,
            health: 100,
            skyblockXP: 0,
            skills: {},
            inventory: {},
            collections: {}
        };
        
        this.currentCollectionTab = 'FARMING';
        this.isInitialized = false;
        this.panelStates = {
            'skills-panel': true,
            'inventory-panel': true,
            'collections-panel': true,
            'activity-log-panel': true
        };
    }

    async initialize() {
        if (this.isInitialized) return;
        
        // Load all game data
        const success = await window.dataLoader.loadAllData();
        if (!success) {
            console.error('Failed to load game data');
            return;
        }

        // Initialize 3D world
        window.dataLoader.updateLoadingProgress(95, "Vytváření 3D světa...");
        await window.world3D.initialize();

        // Initialize player skills
        const skills = window.dataLoader.getAllSkills();
        for (const skillKey of Object.keys(skills)) {
            if (!this.playerData.skills[skillKey]) {
                this.playerData.skills[skillKey] = {
                    xp: 0,
                    level: 0
                };
            }
        }

        // Initialize collections
        const collections = window.dataLoader.getAllCollections();
        for (const [categoryKey, categoryData] of Object.entries(collections)) {
            if (!this.playerData.collections[categoryKey]) {
                this.playerData.collections[categoryKey] = {};
            }
            
            if (categoryData.items) {
                for (const itemKey of Object.keys(categoryData.items)) {
                    if (!this.playerData.collections[categoryKey][itemKey]) {
                        this.playerData.collections[categoryKey][itemKey] = {
                            collected: 0,
                            tier: 0
                        };
                    }
                }
            }
        }

        this.isInitialized = true;
        
        // Hide loading screen
        setTimeout(() => {
            document.getElementById('loading-screen').style.opacity = '0';
            setTimeout(() => {
                document.getElementById('loading-screen').style.display = 'none';
            }, 500);
        }, 1000);
        
        this.updateUI();
        this.addLogEntry('Vítej v SkyBlock 3D Adventure! 🎮');
        this.addLogEntry('Použij WASD a myš pro pohyb kamery 🎥');
        this.addLogEntry('Přiblíž se k různým oblastem a stiskni E pro interakci ⚡');
    }

    performAction(skillKey) {
        if (!this.isInitialized) return;

        const baseXP = 10 + Math.floor(Math.random() * 15); // 10-24 XP
        const oldLevel = this.playerData.skills[skillKey].level;
        
        // Add XP
        this.playerData.skills[skillKey].xp += baseXP;
        
        // Check for level up
        const newLevel = window.dataLoader.getLevelFromXP(skillKey, this.playerData.skills[skillKey].xp);
        if (newLevel > oldLevel) {
            this.levelUp(skillKey, oldLevel, newLevel);
        }
        
        this.playerData.skills[skillKey].level = newLevel;

        // Chance to get item (30% chance)
        if (Math.random() < 0.3) {
            const item = window.dataLoader.getRandomActivityItem(skillKey);
            if (item) {
                this.addItemToInventory(item);
            }
        }

        this.addLogEntry(`+${baseXP} ${skillKey} XP získáno! 💪`);
        this.updateUI();
        
        // Add visual effect in 3D world
        this.createXPEffect(skillKey, baseXP);
    }

    createXPEffect(skillKey, xp) {
        // Create floating XP text effect in 3D world
        if (window.world3D && window.world3D.scene) {
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 64;
            
            context.fillStyle = '#FFD700';
            context.font = 'Bold 24px Arial';
            context.textAlign = 'center';
            context.fillText(`+${xp} XP`, 128, 40);
            
            const texture = new THREE.CanvasTexture(canvas);
            const material = new THREE.SpriteMaterial({ map: texture });
            const sprite = new THREE.Sprite(material);
            
            sprite.position.copy(window.world3D.camera.position);
            sprite.position.y += 2;
            sprite.scale.set(4, 1, 1);
            
            window.world3D.scene.add(sprite);
            
            // Animate and remove
            let opacity = 1;
            const animate = () => {
                sprite.position.y += 0.1;
                opacity -= 0.02;
                sprite.material.opacity = opacity;
                
                if (opacity > 0) {
                    requestAnimationFrame(animate);
                } else {
                    window.world3D.scene.remove(sprite);
                }
            };
            animate();
        }
    }

    levelUp(skillKey, oldLevel, newLevel) {
        for (let level = oldLevel + 1; level <= newLevel; level++) {
            const unlocks = window.dataLoader.getUnlocksForLevel(skillKey, level);
            
            // Process unlocks
            unlocks.forEach(unlock => {
                if (unlock.includes('Coins')) {
                    const coins = parseInt(unlock.match(/\+?([\d,]+)\s*Coins/)?.[1]?.replace(/,/g, '') || 0);
                    this.playerData.coins += coins;
                }
                
                if (unlock.includes('Health')) {
                    const health = parseInt(unlock.match(/\+?(\d+)\s*❤?\s*Health/)?.[1] || 0);
                    this.playerData.health += health;
                }
                
                if (unlock.includes('SkyBlock XP')) {
                    const xp = parseInt(unlock.match(/\+?(\d+)\s*SkyBlock XP/)?.[1] || 0);
                    this.playerData.skyblockXP += xp;
                }
            });
            
            this.addLogEntry(`🎉 ${skillKey} Level ${level} dosažen!`);
            
            if (unlocks.length > 0) {
                this.addLogEntry(`🎁 Odměny: ${unlocks.slice(0, 3).join(', ')}${unlocks.length > 3 ? '...' : ''}`);
            }
            
            // Create level up effect
            this.createLevelUpEffect(skillKey, level);
        }
    }

    createLevelUpEffect(skillKey, level) {
        // Create spectacular level up effect in 3D world
        if (window.world3D && window.world3D.scene) {
            // Particle explosion effect
            const particleCount = 50;
            const particles = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            
            for (let i = 0; i < particleCount; i++) {
                positions[i * 3] = (Math.random() - 0.5) * 10;
                positions[i * 3 + 1] = Math.random() * 5;
                positions[i * 3 + 2] = (Math.random() - 0.5) * 10;
                
                colors[i * 3] = 1; // R
                colors[i * 3 + 1] = 0.8; // G
                colors[i * 3 + 2] = 0; // B
            }
            
            particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            const particleMaterial = new THREE.PointsMaterial({
                size: 0.5,
                vertexColors: true,
                transparent: true,
                opacity: 1
            });
            
            const particleSystem = new THREE.Points(particles, particleMaterial);
            particleSystem.position.copy(window.world3D.camera.position);
            window.world3D.scene.add(particleSystem);
            
            // Animate particles
            let time = 0;
            const animateParticles = () => {
                time += 0.1;
                particleSystem.rotation.y += 0.1;
                particleMaterial.opacity = Math.max(0, 1 - time / 3);
                
                if (particleMaterial.opacity > 0) {
                    requestAnimationFrame(animateParticles);
                } else {
                    window.world3D.scene.remove(particleSystem);
                }
            };
            animateParticles();
        }
    }

    addItemToInventory(item) {
        const itemId = item.id;
        if (!this.playerData.inventory[itemId]) {
            this.playerData.inventory[itemId] = {
                item: item,
                count: 0
            };
        }
        
        this.playerData.inventory[itemId].count++;
        this.addLogEntry(`📦 Získán předmět: ${item.name} (${item.tier})`);
        
        // Update collections if applicable
        this.updateCollections(item);
    }

    updateCollections(item) {
        // Find which collection this item belongs to
        const collections = window.dataLoader.getAllCollections();
        
        for (const [categoryKey, categoryData] of Object.entries(collections)) {
            if (categoryData.items) {
                for (const [itemKey, itemData] of Object.entries(categoryData.items)) {
                    // Simple matching by name or ID
                    if (itemKey.includes(item.material) || item.name.toLowerCase().includes(itemData.name.toLowerCase())) {
                        if (!this.playerData.collections[categoryKey]) {
                            this.playerData.collections[categoryKey] = {};
                        }
                        
                        if (!this.playerData.collections[categoryKey][itemKey]) {
                            this.playerData.collections[categoryKey][itemKey] = {
                                collected: 0,
                                tier: 0
                            };
                        }
                        
                        this.playerData.collections[categoryKey][itemKey].collected++;
                        
                        // Check for tier progression
                        const collectionItem = this.playerData.collections[categoryKey][itemKey];
                        const currentTier = collectionItem.tier;
                        const newTier = this.calculateCollectionTier(itemData, collectionItem.collected);
                        
                        if (newTier > currentTier) {
                            collectionItem.tier = newTier;
                            this.addLogEntry(`🏆 ${itemData.name} kolekce tier ${newTier} dosažen!`);
                        }
                        
                        return;
                    }
                }
            }
        }
    }

    calculateCollectionTier(itemData, collected) {
        if (!itemData.tiers) return 0;
        
        let tier = 0;
        for (const tierData of itemData.tiers) {
            if (collected >= tierData.amountRequired) {
                tier = tierData.tier;
            } else {
                break;
            }
        }
        
        return tier;
    }

    sellAllItems() {
        let totalCoins = 0;
        let itemCount = 0;
        
        for (const [itemId, inventoryItem] of Object.entries(this.playerData.inventory)) {
            const sellPrice = inventoryItem.item.npc_sell_price || 1;
            const coins = sellPrice * inventoryItem.count;
            totalCoins += coins;
            itemCount += inventoryItem.count;
        }
        
        this.playerData.inventory = {};
        this.playerData.coins += totalCoins;
        
        if (itemCount > 0) {
            this.addLogEntry(`💰 Prodáno ${itemCount} předmětů za ${totalCoins} coins!`);
        } else {
            this.addLogEntry('❌ Žádné předměty k prodeji!');
        }
        
        this.updateUI();
    }

    showCollectionTab(categoryKey) {
        this.currentCollectionTab = categoryKey;
        
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        event.target.classList.add('active');
        
        this.updateCollectionsUI();
    }

    updateUI() {
        this.updatePlayerStats();
        this.updateSkillsUI();
        this.updateInventoryUI();
        this.updateCollectionsUI();
    }

    updatePlayerStats() {
        document.getElementById('coins').textContent = this.playerData.coins.toLocaleString();
        document.getElementById('health').textContent = this.playerData.health;
        document.getElementById('skyblock-xp').textContent = this.playerData.skyblockXP.toLocaleString();
    }

    updateSkillsUI() {
        const skillsContent = document.getElementById('skills-content');
        skillsContent.innerHTML = '';
        
        const skills = window.dataLoader.getAllSkills();
        
        for (const [skillKey, skillData] of Object.entries(skills)) {
            const playerSkill = this.playerData.skills[skillKey];
            const currentXP = playerSkill.xp;
            const currentLevel = playerSkill.level;
            const nextLevelXP = window.dataLoader.getXPForLevel(skillKey, currentLevel + 1);
            const currentLevelXP = window.dataLoader.getXPForLevel(skillKey, currentLevel);
            
            const progressPercent = nextLevelXP > 0 ? 
                ((currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100 : 100;
            
            const skillDiv = document.createElement('div');
            skillDiv.className = 'skill-item';
            skillDiv.innerHTML = `
                <div class="skill-header">
                    <span class="skill-name">${skillData.name}</span>
                    <span class="skill-level">Level ${currentLevel}</span>
                </div>
                <div class="skill-progress">
                    <div class="skill-progress-bar" style="width: ${Math.min(progressPercent, 100)}%"></div>
                </div>
                <div class="skill-xp">${currentXP.toLocaleString()} / ${nextLevelXP > 0 ? nextLevelXP.toLocaleString() : 'MAX'} XP</div>
            `;
            
            skillsContent.appendChild(skillDiv);
        }
    }

    updateInventoryUI() {
        const inventoryGrid = document.getElementById('inventory-grid');
        const inventoryCount = document.getElementById('inventory-count');
        
        inventoryGrid.innerHTML = '';
        
        let totalItems = 0;
        
        for (const [itemId, inventoryItem] of Object.entries(this.playerData.inventory)) {
            totalItems += inventoryItem.count;
            
            const itemDiv = document.createElement('div');
            itemDiv.className = 'inventory-item';
            itemDiv.innerHTML = `
                <div class="item-name">${inventoryItem.item.name}</div>
                <div class="item-tier tier-${inventoryItem.item.tier}">${inventoryItem.item.tier}</div>
                <div class="item-count">x${inventoryItem.count}</div>
            `;
            
            inventoryGrid.appendChild(itemDiv);
        }
        
        inventoryCount.textContent = totalItems;
    }

    updateCollectionsUI() {
        const collectionsContent = document.getElementById('collections-content');
        collectionsContent.innerHTML = '';
        
        const categoryData = window.dataLoader.getCollectionData(this.currentCollectionTab);
        if (!categoryData || !categoryData.items) return;
        
        for (const [itemKey, itemData] of Object.entries(categoryData.items)) {
            const playerCollection = this.playerData.collections[this.currentCollectionTab]?.[itemKey] || {
                collected: 0,
                tier: 0
            };
            
            const currentTier = playerCollection.tier;
            const nextTierData = itemData.tiers ? itemData.tiers.find(t => t.tier === currentTier + 1) : null;
            const progressPercent = nextTierData ? 
                (playerCollection.collected / nextTierData.amountRequired) * 100 : 100;
            
            const collectionDiv = document.createElement('div');
            collectionDiv.className = 'collection-item';
            collectionDiv.innerHTML = `
                <div class="collection-header">
                    <span class="collection-name">${itemData.name}</span>
                    <span class="collection-tier">Tier ${currentTier}/${itemData.maxTiers || 0}</span>
                </div>
                <div class="collection-progress">
                    <div class="collection-progress-bar" style="width: ${Math.min(progressPercent, 100)}%"></div>
                </div>
                <div class="collection-count">
                    ${playerCollection.collected.toLocaleString()} / ${nextTierData ? nextTierData.amountRequired.toLocaleString() : 'MAX'}
                </div>
            `;
            
            collectionsContent.appendChild(collectionDiv);
        }
    }

    addLogEntry(message) {
        const logEntries = document.getElementById('log-entries');
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        
        logEntries.insertBefore(logEntry, logEntries.firstChild);
        
        // Keep only last 50 entries
        while (logEntries.children.length > 50) {
            logEntries.removeChild(logEntries.lastChild);
        }
    }

    saveGame() {
        try {
            localStorage.setItem('skyblock-3d-adventure-save', JSON.stringify(this.playerData));
            this.addLogEntry('💾 Hra byla uložena!');
        } catch (error) {
            console.error('Error saving game:', error);
            this.addLogEntry('❌ Chyba při ukládání hry!');
        }
    }

    loadGame() {
        try {
            const saveData = localStorage.getItem('skyblock-3d-adventure-save');
            if (saveData) {
                this.playerData = JSON.parse(saveData);
                this.updateUI();
                this.addLogEntry('📁 Hra byla načtena!');
            } else {
                this.addLogEntry('❌ Žádný uložený soubor nenalezen!');
            }
        } catch (error) {
            console.error('Error loading game:', error);
            this.addLogEntry('❌ Chyba při načítání hry!');
        }
    }

    resetGame() {
        if (confirm('Opravdu chcete resetovat hru? Všechen pokrok bude ztracen!')) {
            localStorage.removeItem('skyblock-3d-adventure-save');
            location.reload();
        }
    }
}

// Global game instance
window.game = new SkyBlock3DGame();

// Global functions for HTML onclick events
function togglePanel(panelId) {
    const panel = document.getElementById(panelId);
    panel.classList.toggle('collapsed');
    
    const toggle = panel.querySelector('.panel-toggle');
    toggle.textContent = panel.classList.contains('collapsed') ? '+' : '−';
}

function sellAllItems() {
    window.game.sellAllItems();
}

function showCollectionTab(categoryKey) {
    window.game.showCollectionTab(categoryKey);
}

function saveGame() {
    window.game.saveGame();
}

function loadGame() {
    window.game.loadGame();
}

function resetGame() {
    window.game.resetGame();
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function toggleControls() {
    const controlsHelp = document.getElementById('controls-help');
    controlsHelp.style.display = controlsHelp.style.display === 'block' ? 'none' : 'block';
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game.initialize();
});
