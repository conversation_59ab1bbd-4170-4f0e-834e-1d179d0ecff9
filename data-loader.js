// Data Loader for SkyBlock 3D Adventure
class DataLoader {
    constructor() {
        this.skillsData = null;
        this.itemsData = null;
        this.collectionsData = null;
        this.loadingProgress = 0;
    }

    async loadAllData() {
        try {
            this.updateLoadingProgress(10, "Načít<PERSON><PERSON> dovedností...");
            this.skillsData = await this.loadJSON('skills.json');
            
            this.updateLoadingProgress(40, "Načítání předmětů...");
            this.itemsData = await this.loadJSON('items.json');
            
            this.updateLoadingProgress(70, "Načítání kolekcí...");
            this.collectionsData = await this.loadJSON('collections.json');
            
            this.updateLoadingProgress(90, "Zpracování dat...");
            this.processData();
            
            this.updateLoadingProgress(100, "Hotovo!");
            
            return true;
        } catch (error) {
            console.error('Chyba při na<PERSON> dat:', error);
            this.updateLoadingProgress(0, "Chyba při načítání!");
            return false;
        }
    }

    async loadJSON(filename) {
        try {
            const response = await fetch(filename);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Chyba při načítání ${filename}:`, error);
            throw error;
        }
    }

    updateLoadingProgress(percentage, text) {
        const progressBar = document.getElementById('loading-progress');
        const loadingText = document.getElementById('loading-text');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (loadingText) {
            loadingText.textContent = text;
        }
        
        this.loadingProgress = percentage;
    }

    processData() {
        // Process skills data for easier access
        if (this.skillsData && this.skillsData.skills) {
            this.processedSkills = {};
            for (const [skillKey, skillData] of Object.entries(this.skillsData.skills)) {
                this.processedSkills[skillKey] = {
                    ...skillData,
                    key: skillKey
                };
            }
        }

        // Process items data for easier access
        if (this.itemsData && this.itemsData.items) {
            this.processedItems = {};
            this.itemsByCategory = {};
            
            this.itemsData.items.forEach(item => {
                this.processedItems[item.id] = item;
                
                if (!this.itemsByCategory[item.category]) {
                    this.itemsByCategory[item.category] = [];
                }
                this.itemsByCategory[item.category].push(item);
            });
        }

        // Process collections data for easier access
        if (this.collectionsData && this.collectionsData.collections) {
            this.processedCollections = {};
            for (const [categoryKey, categoryData] of Object.entries(this.collectionsData.collections)) {
                this.processedCollections[categoryKey] = {
                    ...categoryData,
                    key: categoryKey
                };
            }
        }
    }

    getSkillData(skillKey) {
        return this.processedSkills ? this.processedSkills[skillKey] : null;
    }

    getAllSkills() {
        return this.processedSkills || {};
    }

    getItemData(itemId) {
        return this.processedItems ? this.processedItems[itemId] : null;
    }

    getItemsByCategory(category) {
        return this.itemsByCategory ? this.itemsByCategory[category] || [] : [];
    }

    getRandomItemByCategory(category) {
        const items = this.getItemsByCategory(category);
        if (items.length === 0) return null;
        
        // Weight items by rarity (lower tier = higher chance)
        const weightedItems = [];
        items.forEach(item => {
            let weight = 1;
            switch (item.tier) {
                case 'COMMON': weight = 50; break;
                case 'UNCOMMON': weight = 25; break;
                case 'RARE': weight = 10; break;
                case 'EPIC': weight = 5; break;
                case 'LEGENDARY': weight = 2; break;
                case 'MYTHIC': weight = 1; break;
                default: weight = 30; break;
            }
            
            for (let i = 0; i < weight; i++) {
                weightedItems.push(item);
            }
        });
        
        return weightedItems[Math.floor(Math.random() * weightedItems.length)];
    }

    getCollectionData(categoryKey) {
        return this.processedCollections ? this.processedCollections[categoryKey] : null;
    }

    getAllCollections() {
        return this.processedCollections || {};
    }

    // Get items that can be obtained from specific activities
    getActivityItems(skillKey) {
        const categoryMap = {
            'FARMING': ['FARMING', 'SEEDS', 'CROP'],
            'MINING': ['MINING', 'ORE', 'GEMSTONE', 'REFORGE_STONE'],
            'COMBAT': ['WEAPON', 'ARMOR', 'ACCESSORY', 'COMBAT'],
            'FORAGING': ['FORAGING', 'WOOD', 'LOG'],
            'FISHING': ['FISHING', 'FISH', 'BAIT'],
            'ENCHANTING': ['ENCHANTING', 'ENCHANTED_BOOK', 'RUNE']
        };

        const categories = categoryMap[skillKey] || [];
        let items = [];
        
        categories.forEach(category => {
            items = items.concat(this.getItemsByCategory(category));
        });
        
        return items;
    }

    // Get random item for activity
    getRandomActivityItem(skillKey) {
        const items = this.getActivityItems(skillKey);
        if (items.length === 0) return null;
        
        // Weight by rarity
        const weightedItems = [];
        items.forEach(item => {
            let weight = 1;
            switch (item.tier) {
                case 'COMMON': weight = 40; break;
                case 'UNCOMMON': weight = 20; break;
                case 'RARE': weight = 8; break;
                case 'EPIC': weight = 4; break;
                case 'LEGENDARY': weight = 2; break;
                case 'MYTHIC': weight = 1; break;
                default: weight = 25; break;
            }
            
            for (let i = 0; i < weight; i++) {
                weightedItems.push(item);
            }
        });
        
        return weightedItems[Math.floor(Math.random() * weightedItems.length)];
    }

    // Calculate XP needed for next level
    getXPForLevel(skillKey, level) {
        const skillData = this.getSkillData(skillKey);
        if (!skillData || !skillData.levels) return 0;
        
        const levelData = skillData.levels.find(l => l.level === level);
        return levelData ? levelData.totalExpRequired : 0;
    }

    // Get level from XP
    getLevelFromXP(skillKey, xp) {
        const skillData = this.getSkillData(skillKey);
        if (!skillData || !skillData.levels) return 0;
        
        let level = 0;
        for (const levelData of skillData.levels) {
            if (xp >= levelData.totalExpRequired) {
                level = levelData.level;
            } else {
                break;
            }
        }
        
        return level;
    }

    // Get unlocks for level
    getUnlocksForLevel(skillKey, level) {
        const skillData = this.getSkillData(skillKey);
        if (!skillData || !skillData.levels) return [];
        
        const levelData = skillData.levels.find(l => l.level === level);
        return levelData ? levelData.unlocks : [];
    }

    // Get 3D model info for items (for future 3D item rendering)
    getItemModel(itemId) {
        const item = this.getItemData(itemId);
        if (!item) return null;
        
        // Return basic 3D model info based on item category
        const modelMap = {
            'WEAPON': { type: 'sword', color: '#silver' },
            'ARMOR': { type: 'armor', color: '#brown' },
            'TOOL': { type: 'tool', color: '#gray' },
            'FARMING': { type: 'crop', color: '#green' },
            'MINING': { type: 'ore', color: '#blue' },
            'FISH': { type: 'fish', color: '#cyan' },
            'WOOD': { type: 'log', color: '#brown' },
            'GEMSTONE': { type: 'gem', color: '#purple' }
        };
        
        return modelMap[item.category] || { type: 'cube', color: '#white' };
    }
}

// Global data loader instance
window.dataLoader = new DataLoader();
