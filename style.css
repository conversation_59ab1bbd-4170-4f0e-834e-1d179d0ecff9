/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #000;
    overflow: hidden;
    color: white;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h1 {
    font-size: 3em;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading-bar {
    width: 400px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.loading-content p {
    font-size: 1.2em;
    margin-top: 20px;
}

/* Game Container */
.game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* UI Overlay */
.ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.ui-overlay > * {
    pointer-events: auto;
}

/* Top HUD */
.top-hud {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1001;
}

.player-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    backdrop-filter: blur(10px);
}

.stat-icon {
    font-size: 1.5em;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.2em;
    font-weight: bold;
    color: #ffd700;
}

.stat-label {
    font-size: 0.8em;
    color: #ccc;
}

.game-title h1 {
    font-size: 2em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    color: #ffd700;
}

.time-weather {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 10px 15px;
    backdrop-filter: blur(10px);
}

.time-display {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2em;
    font-weight: bold;
}

/* Panels */
.panel {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
    max-height: 400px;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
    color: #ffd700;
    font-size: 1.2em;
}

.panel-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.panel-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.panel-content {
    padding: 15px 20px;
    max-height: 320px;
    overflow-y: auto;
}

/* Skills Panel */
.skills-panel {
    top: 100px;
    left: 20px;
    width: 300px;
}

.skill-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.skill-name {
    font-weight: bold;
    color: #fff;
}

.skill-level {
    background: #4299e1;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
}

.skill-progress {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 15px;
    overflow: hidden;
    margin-bottom: 5px;
}

.skill-progress-bar {
    background: linear-gradient(90deg, #4299e1, #63b3ed);
    height: 100%;
    transition: width 0.3s ease;
}

.skill-xp {
    font-size: 0.8em;
    color: #ccc;
    text-align: center;
}

/* Inventory Panel */
.inventory-panel {
    top: 100px;
    right: 20px;
    width: 300px;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.inventory-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.inventory-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.item-name {
    font-size: 0.7em;
    font-weight: bold;
    margin-bottom: 3px;
    color: white;
}

.item-tier {
    font-size: 0.6em;
    padding: 1px 4px;
    border-radius: 8px;
    color: white;
    margin-bottom: 3px;
}

.item-count {
    font-size: 0.6em;
    color: #ccc;
}

/* Tier Colors */
.tier-COMMON { background: #9ca3af; }
.tier-UNCOMMON { background: #10b981; }
.tier-RARE { background: #3b82f6; }
.tier-EPIC { background: #8b5cf6; }
.tier-LEGENDARY { background: #f59e0b; }
.tier-MYTHIC { background: #ef4444; }

.inventory-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.item-count {
    color: #ccc;
    font-size: 0.9em;
}

/* Collections Panel */
.collections-panel {
    bottom: 20px;
    left: 20px;
    width: 350px;
}

.collections-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2em;
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.collection-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
}

.collection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.collection-name {
    font-weight: bold;
    color: white;
}

.collection-tier {
    background: #10b981;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.8em;
}

.collection-progress {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    height: 12px;
    overflow: hidden;
    margin-bottom: 5px;
}

.collection-progress-bar {
    background: linear-gradient(90deg, #10b981, #34d399);
    height: 100%;
    transition: width 0.3s ease;
}

.collection-count {
    font-size: 0.8em;
    color: #ccc;
    text-align: center;
}

/* Activity Log */
.activity-log {
    bottom: 20px;
    right: 20px;
    width: 300px;
}

.log-entries {
    max-height: 250px;
    overflow-y: auto;
}

.log-entry {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.8em;
    color: #ccc;
}

.log-entry:last-child {
    border-bottom: none;
}

/* Interaction Prompt */
.interaction-prompt {
    position: absolute;
    bottom: 50%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 15px 25px;
    display: none;
    backdrop-filter: blur(10px);
}

.prompt-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.prompt-icon {
    font-size: 2em;
}

.prompt-text {
    font-size: 1.2em;
    font-weight: bold;
    color: #ffd700;
}

/* Controls Help */
.controls-help {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    display: none;
    backdrop-filter: blur(15px);
    max-width: 400px;
}

.controls-content h4 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-item:last-child {
    border-bottom: none;
}

.key {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    color: #ffd700;
}

.desc {
    color: #ccc;
}

/* Bottom Controls */
.bottom-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.control-btn {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Panel Collapsed State */
.panel.collapsed .panel-content {
    display: none;
}

.panel.collapsed {
    max-height: 60px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .top-hud {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .player-stats {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .panel {
        width: 280px !important;
        max-height: 300px;
    }
    
    .skills-panel,
    .collections-panel {
        left: 10px;
    }
    
    .inventory-panel,
    .activity-log {
        right: 10px;
    }
    
    .bottom-controls {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .control-btn {
        padding: 6px 10px;
        font-size: 0.9em;
    }
}
