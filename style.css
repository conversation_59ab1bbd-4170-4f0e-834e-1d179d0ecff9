/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Game Container */
.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.game-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.game-header h1 {
    font-size: 2.5em;
    color: #4a5568;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.player-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.stat {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-icon {
    font-size: 1.2em;
}

/* Game Content */
.game-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Sections */
section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.5em;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

/* Skills Section */
.skills-section {
    grid-column: 1 / -1;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.skill-card {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.skill-name {
    font-weight: bold;
    font-size: 1.1em;
    color: #2d3748;
}

.skill-level {
    background: #4299e1;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.9em;
}

.skill-progress {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin-bottom: 5px;
}

.skill-progress-bar {
    background: linear-gradient(90deg, #4299e1, #63b3ed);
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.skill-xp {
    font-size: 0.9em;
    color: #4a5568;
    text-align: center;
}

/* Actions Section */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.action-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 15px;
    padding: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-btn:active {
    transform: translateY(0);
}

.action-icon {
    font-size: 2em;
}

.action-name {
    font-weight: bold;
    font-size: 1.1em;
}

.action-desc {
    font-size: 0.9em;
    opacity: 0.9;
}

/* Inventory Section */
.inventory-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.sell-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    color: white;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.sell-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.inventory-count {
    color: #4a5568;
    font-weight: bold;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.inventory-item {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.inventory-item:hover {
    transform: scale(1.05);
    border-color: #4299e1;
}

.item-name {
    font-size: 0.9em;
    font-weight: bold;
    margin-bottom: 5px;
    color: #2d3748;
}

.item-tier {
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 10px;
    color: white;
    margin-bottom: 5px;
}

.item-count {
    font-size: 0.8em;
    color: #4a5568;
}

/* Tier Colors */
.tier-COMMON { background: #9ca3af; }
.tier-UNCOMMON { background: #10b981; }
.tier-RARE { background: #3b82f6; }
.tier-EPIC { background: #8b5cf6; }
.tier-LEGENDARY { background: #f59e0b; }
.tier-MYTHIC { background: #ef4444; }

/* Collections Section */
.collections-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tab-btn {
    background: #e2e8f0;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.collections-content {
    max-height: 400px;
    overflow-y: auto;
}

.collection-item {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.collection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.collection-progress {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    height: 15px;
    overflow: hidden;
}

.collection-progress-bar {
    background: linear-gradient(90deg, #10b981, #34d399);
    height: 100%;
    transition: width 0.3s ease;
}

/* Activity Log */
.log-section {
    grid-column: 1 / -1;
}

.activity-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f7fafc;
    border-radius: 8px;
    padding: 15px;
}

.log-entry {
    padding: 5px 0;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.9em;
    color: #4a5568;
}

.log-entry:last-child {
    border-bottom: none;
}

/* Save Controls */
.save-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.save-btn, .load-btn, .reset-btn {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    color: white;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.reset-btn {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
}

.save-btn:hover, .load-btn:hover, .reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h2 {
    font-size: 2em;
    margin-bottom: 20px;
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
    width: 0%;
    transition: width 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-content {
        grid-template-columns: 1fr;
    }
    
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .player-stats {
        justify-content: center;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
}
