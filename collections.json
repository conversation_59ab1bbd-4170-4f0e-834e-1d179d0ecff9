{"success": true, "lastUpdated": 1752265705947, "version": "********", "collections": {"FARMING": {"name": "Farming", "items": {"INK_SACK:3": {"name": "Cocoa Beans", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 75, "unlocks": ["Cocoa Beans Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 200, "unlocks": ["+20 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["Portal to Mushroom Island Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2000, "unlocks": ["Enchanted Cocoa Beans Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Travel Scroll to Mushroom Island Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Enchanted <PERSON>ie <PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 20000, "unlocks": ["Adrenaline Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Enchanted Book (Replenish I) Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}]}, "CARROT_ITEM": {"name": "Carrot", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Carrot Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Simple Carrot Candy Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["Carrot Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1750, "unlocks": ["Enchanted Carrot Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Enchanted Carrot on a Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Great Carrot Candy Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Enchanted Golden Carrot Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Superb Carrot Candy Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}]}, "CACTUS": {"name": "Cactus", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Cactus Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Cactus He<PERSON> Recipe", "Cactus Chestplate Recipe", "<PERSON>act<PERSON> Le<PERSON> Recipe", "Cactus Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["Resistance Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Cactus Green Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Desert Island Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Piercing Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Thorns Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Cactus Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["+5,000 Farming Experience", "+4 SkyBlock XP"]}]}, "RAW_CHICKEN": {"name": "Raw Chicken", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Chicken Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Bridge Egg Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Chicken Recipe", "Chicken Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Raw Chicken Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Egg Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["+5,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Cake Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Agility Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Super Enchanted Egg Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Omega Enchanted Egg Recipe", "+4 SkyBlock XP"]}]}, "SUGAR_CANE": {"name": "Sugar Cane", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Sugar Cane Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Speed Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["+500 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Sugar Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2000, "unlocks": ["Enchanted Paper Recipe", "Speed Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Enchanted Bookshelf Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 20000, "unlocks": ["Enchanted Sugar Cane Recipe", "Speed Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Speedster <PERSON><PERSON><PERSON> Recipe", "Speedster Chestplate Recipe", "Speedster Leggings Recipe", "Speedster Boots Recipe", "+4 SkyBlock XP"]}]}, "PUMPKIN": {"name": "<PERSON><PERSON><PERSON>", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 40, "unlocks": ["<PERSON><PERSON><PERSON> Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["<PERSON><PERSON><PERSON> Helmet Reci<PERSON>", "Pumpkin Chestplate Recipe", "<PERSON><PERSON><PERSON> Leggings Recipe", "P<PERSON>kin Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Pumpkin Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Cubism Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Training Dummy Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["<PERSON> <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Lantern Helmet Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Farm Crystal Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Farmer Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Polished <PERSON><PERSON><PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 250000, "unlocks": ["Rancher's Boots Recipe", "+4 SkyBlock XP"]}]}, "WHEAT": {"name": "Wheat", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Wheat Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Harvesting Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Farm Suit Helmet Recipe", "Farm Suit Chestplate Recipe", "Farm Suit Leggings Recipe", "Farm Suit Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Farming Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Wheat Recipe", "Enchanted Bread Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["Farming Island Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Small Agronomy Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 15000, "unlocks": ["Medium Agronomy Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Farm Armor Helmet Recipe", "Farm Armor Chestplate Recipe", "Farm Armor Leggings Recipe", "Farm Armor Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 50000, "unlocks": ["Large Agronomy Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 100000, "unlocks": ["Enchanted Hay Bale Recipe", "Large Enchanted Agronomy Sack Recipe", "+4 SkyBlock XP"]}]}, "SEEDS": {"name": "Seeds", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Dirt Trade", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Clay Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Long Grass Trade", "Enchanted Seeds Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Fern Trade", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Dead Bush Trade", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Double Tallgrass Trade", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Box of Seeds Recipe", "+4 SkyBlock XP"]}]}, "MUSHROOM_COLLECTION": {"name": "Mushroom", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Mushroom Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Mushroom Helmet Recipe", "Mushroom Chestplate Recipe", "Mushroom Leggings Recipe", "Mushroom Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Bat Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Magical Mushroom Soup Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Brown Mushroom Block Recipe", "Red Mushroom Block Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Enchanted Red Mushroom Recipe", "Enchanted Brown Mushroom Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Night Vision Charm Recipe", "Mystical Mushroom Soup Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Brown Mushroom Block Recipe", "Enchanted Red Mushroom Block Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}]}, "RABBIT": {"name": "Raw Rabbit", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Rabbit Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["+100 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Rabbit Potion Recipe", "[Lvl 1] Rabbit Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Rabbit Foot Recipe", "Enchanted Raw Rabbit Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Luck Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Enchanted Rabbit Hide Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Luck of the Sea Exp Discount (-25%)", "Enchanted Cooked Rabbit Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Pet Luck Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Rabbit Helmet Recipe", "Rabbit Chestplate Recipe", "Rabbit Leggings Recipe", "<PERSON> Recipe", "+4 SkyBlock XP"]}]}, "NETHER_STALK": {"name": "Nether Wart", "maxTiers": 12, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Nether Wart Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Small Potion Bag", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Potion Affinity Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Nether Wart Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Medium Potion Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["+500 Alchemy Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Potion Affinity Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Large Potion Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Potion Affinity Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 75000, "unlocks": ["Giant Potion Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 100000, "unlocks": ["<PERSON><PERSON> <PERSON><PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 12, "amountRequired": 250000, "unlocks": ["Massive Potion Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}]}, "MUTTON": {"name": "<PERSON><PERSON>", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Sheep <PERSON> Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["+10 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Sheep Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Mana Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Mutton Recipe", "Small Husbandry Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Rainbow Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Medium Husbandry Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Cooked Mutton Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Horns of Torment Recipe", "Large Husbandry Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Large Enchanted Husbandry Sack Recipe", "+4 SkyBlock XP"]}]}, "MELON": {"name": "Melon", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 250, "unlocks": ["Melon Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 500, "unlocks": ["+50 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1250, "unlocks": ["+125 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 5000, "unlocks": ["Enchanted Melon Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 15000, "unlocks": ["Enchanted Glistering <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["Enchanted Melon Block Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 50000, "unlocks": ["+5,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 100000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 250000, "unlocks": ["<PERSON><PERSON> Helmet Recipe", "Melon Chestplate Recipe", "Melon Leggings Recipe", "Melon Boots Recipe", "+4 SkyBlock XP"]}]}, "POTATO_ITEM": {"name": "Potato", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Potato Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 200, "unlocks": ["Portal to The Barn Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["<PERSON><PERSON>cine Ta<PERSON>man Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1750, "unlocks": ["Enchanted Potato Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Venomous Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Travel Scroll to The Barn Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Enchanted Baked Potato Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Hot Potato Book Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}]}, "LEATHER": {"name": "Leather", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Cow Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Cow Hat Recipe", "Milk Bucket Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Horse Recipe", "Small Backpack Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Enchanted Leather Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Raw Beef Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["Medium Backpack Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["+5,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["Saddle Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Large Backpack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 50000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 100000, "unlocks": ["Greater Backpack Recipe", "+4 SkyBlock XP"]}]}, "PORK": {"name": "Raw Porkchop", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Pig Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["+10 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Pig Recipe", "[Lvl 1] Pigman Recipe", "Enchanted Pork Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["+100 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Piggy Bank Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["+500 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Grilled Pork Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["+10,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Pigman Sword Recipe", "+4 SkyBlock XP"]}]}, "FEATHER": {"name": "<PERSON><PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Projectile Protection Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Feather Falling Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Archery Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Feather Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Feather Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Dragon Tracer Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Feather Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Snipe Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Feather Artifact Recipe", "+4 SkyBlock XP"]}]}}}, "MINING": {"name": "Mining", "items": {"INK_SACK:4": {"name": "<PERSON><PERSON>", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 250, "unlocks": ["Lapis Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 500, "unlocks": ["Experience Bottle Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Lapis Pickaxe Recipe", "Experience Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2000, "unlocks": ["Enchanted Lapis Lazuli Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 10000, "unlocks": ["Grand Experience Bottle Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["Experience Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 50000, "unlocks": ["Enchanted Lapis Block Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 100000, "unlocks": ["Titanic Experience Bottle Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 150000, "unlocks": ["Experience Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 250000, "unlocks": ["Textbook Recipe", "+4 SkyBlock XP"]}]}, "REDSTONE": {"name": "Redstone", "maxTiers": 16, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Redstone Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Small Accessory Bag", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 750, "unlocks": ["Efficiency Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1500, "unlocks": ["Enchanted Redstone Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 3000, "unlocks": ["Weather Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Medium Accessory Bag Upgrade (+6 slots)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Travel Scroll to Deep Caverns Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Redstone Block Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Large Accessory Bag Upgrade (+6 slots)", "Personal Compactor 4000 Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 200000, "unlocks": ["Greater Accessory Bag Upgrade (+6 slots)", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 400000, "unlocks": ["Giant Accessory Bag Upgrade (+6 slots)", "Personal Compactor 5000 Recipe", "+4 SkyBlock XP"]}, {"tier": 12, "amountRequired": 600000, "unlocks": ["Massive Accessory Bag Upgrade (+6 slots)", "+4 SkyBlock XP"]}, {"tier": 13, "amountRequired": 800000, "unlocks": ["Humongous Accessory Bag Upgrade (+6 slots)", "Personal Compactor 6000 Recipe", "+4 SkyBlock XP"]}, {"tier": 14, "amountRequired": 1000000, "unlocks": ["Colossal Accessory Bag Upgrade (+6 slots)", "Personal Compactor 7000 Recipe", "+4 SkyBlock XP"]}, {"tier": 15, "amountRequired": 1200000, "unlocks": ["Titanic Accessory Bag Upgrade (+6 slots)", "+4 SkyBlock XP"]}, {"tier": 16, "amountRequired": 1400000, "unlocks": ["Preposterous Accessory Bag Upgrade (+6 slots)", "+4 SkyBlock XP"]}]}, "UMBER": {"name": "Umber", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Umber Dice (COMING SOON)", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Umber Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 5000, "unlocks": ["Refined Umber Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 10000, "unlocks": ["Umber Key Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 25000, "unlocks": ["Excavator Potion (COMING SOON)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 50000, "unlocks": ["Umber Plate Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 100000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 250000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 500000, "unlocks": ["Umberella Recipe", "+4 SkyBlock XP"]}]}, "COAL": {"name": "Coal", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Coal Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Smelting Touch Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Coal Recipe", "Haste Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["[Lvl 1] Wither Skeleton Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Charcoal Recipe", "Small Mining Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Travel Scroll to the Gold Mine Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Coal Block Recipe", "Medium Mining Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Lava Bucket Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Large Mining Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Large Enchanted Mining Sack Recipe", "+4 SkyBlock XP"]}]}, "MYCEL": {"name": "Mycelium", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Mycelium Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 500, "unlocks": ["Suspicious Stew Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 750, "unlocks": ["Enchanted Mycelium Recipe", "[Lvl 1] Mooshroom Cow Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Mushroom Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Mycelium Dust Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Corrupt Soil Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 15000, "unlocks": ["Enchanted Mycelium Cube Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["+10,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Shimmering Light Hood Recipe", "Shimmering Light Tunic Recipe", "Shimmering Light Trousers Recipe", "Shimmering Light Slippers Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Gauntlet of Contagion Recipe", "+4 SkyBlock XP"]}]}, "ENDER_STONE": {"name": "End Stone", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["<PERSON> Stone Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["End Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Portal to The End Recipe", "[Lvl 1] Endermite Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted End Stone Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Silence Block Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Haste Block Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["+10,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 15000, "unlocks": ["Travel Scroll to the End Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Catalyst Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 50000, "unlocks": ["End Stone Sword Recipe", "+4 SkyBlock XP"]}]}, "QUARTZ": {"name": "<PERSON><PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Quartz Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Night Saver Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Day Saver Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Quartz Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Minion Expander Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Enchanted Quartz Block Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Night Crystal Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Day Crystal Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Solar Panel Recipe", "+4 SkyBlock XP"]}]}, "SAND": {"name": "Sand", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Sand Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["+250 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Soul Sand Trade", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Desert Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Sand Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["Burning Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Hard Glass Recipe", "+4 SkyBlock XP"]}]}, "IRON_INGOT": {"name": "Iron Ingot", "maxTiers": 12, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Iron Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Golem Hat Recipe", "Prospecting <PERSON><PERSON><PERSON>", "Prospecting Chestplate Recipe", "Prospecting Leggings Recipe", "Prospecting Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Protection Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Iron Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Budget Hopper Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Golem Armor Helmet Recipe", "Golem Armor Chestplate Recipe", "Golem Armor Leggings Recipe", "Golem Armor Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Iron Block Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Golem Sword Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Enchanted Hopper Recipe", "Personal Deletor 4000 Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Personal Deletor 5000 Recipe", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 200000, "unlocks": ["Personal Deletor 6000 Recipe", "+4 SkyBlock XP"]}, {"tier": 12, "amountRequired": 400000, "unlocks": ["Personal Deletor 7000 Recipe", "+4 SkyBlock XP"]}]}, "GEMSTONE_COLLECTION": {"name": "Gemstone", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Small Gemstone Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["❤ Flawed Ruby Gemstone Recipe", "☘ Flawed Jade Gemstone Recipe", "✎ Flawed Sapphire Gemstone Recipe", "❈ Flawed Amethyst Gemstone Recipe", "⸕ Flawed Amber Gemstone Recipe", "✧ Flawed Topaz Gemstone Recipe", "❁ Flawed Jasper Gemstone Recipe", "❂ Flawed Opal Gemstone Recipe", "☠ Flawed Onyx Gemstone Recipe", "☘ Flawed Citrine Gemstone Recipe", "☂ Flawed Aquamarine Gemstone Recipe", "☘ Flawed Peridot Gemstone Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Medium Gemstone Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Talisman of Power Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["❤ Fine Ruby Gemstone Recipe", "☘ Fine Jade Gemstone Recipe", "✎ Fine Sapphire Gemstone Recipe", "❈ Fine Amethyst Gemstone Recipe", "⸕ Fine Amber Gemstone Recipe", "✧ Fine Topaz Gemstone Recipe", "❁ Fine Jasper Gemstone Recipe", "❂ Fine Opal Gemstone Recipe", "☠ Fine Onyx Gemstone Recipe", "☘ Fine Citrine Gemstone Recipe", "☂ Fine Aquamarine Gemstone Recipe", "☘ Fine Peridot Gemstone Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["Ring of Power Recipe", "Large Gemstone Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 100000, "unlocks": ["Ruby Power Scroll Recipe", "Sapphire Power Scroll Recipe", "Jasper Power Scroll Recipe", "Amethyst Power Scroll Recipe", "<PERSON> Scroll Recipe", "Opal Power Scroll Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 250000, "unlocks": ["Enchanted Book (Prismatic I) Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 500000, "unlocks": ["❤ Flawless Ruby Gemstone Recipe", "☘ Flawless Jade Gemstone Recipe", "✎ Flawless Sapphire Gemstone Recipe", "❈ Flawless Amethyst Gemstone Recipe", "⸕ Flawless Amber Gemstone Recipe", "✧ Flawless Topaz Gemstone Recipe", "❁ Flawless Jasper Gemstone Recipe", "❂ Flawless Opal Gemstone Recipe", "☠ Flawless Onyx Gemstone Recipe", "☘ Flawless Citrine Gemstone Recipe", "☂ Flawless Aquamarine Gemstone Recipe", "☘ Flawless Peridot Gemstone Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 1000000, "unlocks": ["Artifact of Power Recipe", "❤ Perfect Ruby Gemstone Dwarven Forge Recipe", "☘ Perfect Jade Gemstone Dwarven Forge Recipe", "✎ Perfect Sapphire Gemstone Dwarven Forge Recipe", "❈ Perfect Amethyst Gemstone Dwarven Forge Recipe", "⸕ Perfect Amber Gemstone Dwarven Forge Recipe", "✧ Perfect Topaz Gemstone Dwarven Forge Recipe", "❁ Perfect Jasper Gemstone Dwarven Forge Recipe", "❂ Perfect Opal Gemstone Dwarven Forge Recipe", "☠ Perfect Onyx Gemstone Dwarven Forge Recipe", "☘ Perfect Citrine Gemstone Dwarven Forge Recipe", "☂ Perfect Aquamarine Gemstone Dwarven Forge Recipe", "☘ Perfect Peridot Gemstone Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 2000000, "unlocks": ["Gemstone Gauntlet Recipe", "+4 SkyBlock XP"]}]}, "TUNGSTEN": {"name": "<PERSON><PERSON><PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["<PERSON><PERSON>warven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Tungsten Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 5000, "unlocks": ["Refined Tungsten Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 10000, "unlocks": ["Tungsten Key Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 25000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 50000, "unlocks": ["Tungsten Plate Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 100000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 250000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 500000, "unlocks": ["Enchanted Book (Metallurgy I) (COMING SOON)", "+4 SkyBlock XP"]}]}, "OBSIDIAN": {"name": "Obsidian", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Obsidian Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Let<PERSON><PERSON> Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Gravity Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Obsidian Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["+5,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Stun Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["+10,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["+25,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Obsidian Tablet Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Reforge Anvil <PERSON>", "+4 SkyBlock XP"]}]}, "DIAMOND": {"name": "Diamond", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Diamond Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Execute Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Portal to the Deep Caverns Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Diamond Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Critical Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Diamond Spreading Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Hardened Diamond Helmet Recipe", "Hardened Diamond Chestplate Recipe", "Hardened Diamond Leggings Recipe", "Hardened Diamond Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Diamond Block Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Perfect Helmet - Tier I Recipe", "Perfect Chestplate - Tier I Recipe", "Perfect Leggings - Tier I Recipe", "Perfect Boots - Tier I Recipe", "+4 SkyBlock XP"]}]}, "COBBLESTONE": {"name": "Cobblestone", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Cobblestone Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Stone Platform Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Silverfish Recipe", "Auto Smelter Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Cobblestone Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Compactor Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["+1,000 Farming Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["<PERSON><PERSON> Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Hyper Furnace Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 40000, "unlocks": ["Haste Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 70000, "unlocks": ["Super Compactor 3000 Recipe", "+4 SkyBlock XP"]}]}, "GLOWSTONE_DUST": {"name": "Glowstone Dust", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Glowstone Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Portal to the Crimson Isle Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Enchanted Glowstone Dust Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Travel Scroll to the Crimson Isle Recipe", "Glowstone Gauntlet Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Enchanted Glowstone Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Enchanted Redstone Lamp Recipe", "Shiny Prism Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Vanquished Glowstone Gauntlet Recipe", "+4 SkyBlock XP"]}]}, "GOLD_INGOT": {"name": "Gold Ingot", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Gold Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Cleaver Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Looting Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Portal to the Gold Mine Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Gold Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["Absorption Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Scavenger Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["Enchanted Gold Block Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Fortune Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 500000, "unlocks": ["Enchanted Clock Recipe", "+4 SkyBlock XP"]}]}, "GRAVEL": {"name": "<PERSON>l", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["<PERSON>l <PERSON>on Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["<PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Portal to the Spider's Den Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Sharpness Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Flint Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["First Strike Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Critical Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 15000, "unlocks": ["Travel Scroll to Spider's Den Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["+5,000 Mining Experience", "+4 SkyBlock XP"]}]}, "HARD_STONE": {"name": "Hard Stone", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Hard Stone Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Heat Helmet Recipe", "Heat Chestplate Recipe", "Heat Leggings Recipe", "Heat Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 5000, "unlocks": ["Enchanted Hard Stone Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 50000, "unlocks": ["Scorched Topaz Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 150000, "unlocks": ["Flamebreaker <PERSON><PERSON><PERSON>", "Flamebreaker Chestplate Recipe", "Flamebreaker Le<PERSON><PERSON> Recipe", "Flamebreaker <PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 300000, "unlocks": ["Concentrated Stone Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 1000000, "unlocks": ["Silex Recipe", "+4 SkyBlock XP"]}]}, "MITHRIL_ORE": {"name": "<PERSON><PERSON><PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["<PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Enchanted <PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 2500, "unlocks": ["[Lvl 1] <PERSON><PERSON><PERSON> Recipe", "Spelunker Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 10000, "unlocks": ["Mithril Crystal Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 50000, "unlocks": ["+5,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 100000, "unlocks": ["Dwarven Super Compactor Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 250000, "unlocks": ["Mithril Coat Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 500000, "unlocks": ["Mithril Infusion Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 1000000, "unlocks": ["Beacon I Recipe", "+4 SkyBlock XP"]}]}, "EMERALD": {"name": "Emerald", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Emerald Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Talisman of Coins Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Magnetic Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Emerald Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Emerald Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 15000, "unlocks": ["Access to /bank (Requires <PERSON><PERSON>)", "Personal Bank Item Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 30000, "unlocks": ["Enchanted Emerald Block Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Emerald Blade Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["Emerald Armor Helmet Recipe", "Emerald Armor Chestplate Recipe", "Emerald Armor Leggings Recipe", "Emerald Armor Boots Recipe", "+4 SkyBlock XP"]}]}, "SAND:1": {"name": "Red Sand", "maxTiers": 8, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Red Sand Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 500, "unlocks": ["Enchanted Red Sand Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 2500, "unlocks": ["[Lvl 1] Snail Recipe", "Mesa Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 10000, "unlocks": ["Enchanted Red Sand Cube Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 15000, "unlocks": ["Ancient Cloak Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["+10,000 Mining Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 50000, "unlocks": ["<PERSON><PERSON><PERSON><PERSON> Helmet Recipe", "Berserker Chestplate Recipe", "<PERSON><PERSON><PERSON><PERSON> Le<PERSON> Recipe", "Berserker Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 100000, "unlocks": ["Everburning Flame Recipe", "+4 SkyBlock XP"]}]}, "ICE": {"name": "Ice", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Ice Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Ice Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Packed Ice Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Enchanted Ice Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Magical Water Bucket Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Glacial Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Packed Ice Recipe", "Ice Cube Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Glacial Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["Frozen Blaze Helmet Recipe", "Frozen Blaze Chestplate Recipe", "Frozen Blaze Leggings Recipe", "Frozen Blaze Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 250000, "unlocks": ["<PERSON><PERSON><PERSON> Scythe <PERSON>", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 500000, "unlocks": ["Glacial <PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}]}, "GLACITE": {"name": "Glacite", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 100, "unlocks": ["Chilled Torch (COMING SOON)", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Glacite Recipe", "Cold Resistance Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 5000, "unlocks": ["Glacite Amalgamation Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 10000, "unlocks": ["[Lvl 1] Glacite Golem Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 25000, "unlocks": ["Frigid Husk Dwarven Forge Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 50000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 100000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 250000, "unlocks": ["+1☘ Mining Fortune", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 500000, "unlocks": ["+10,000 Mining Experience", "+4 SkyBlock XP"]}]}, "SULPHUR_ORE": {"name": "Sulphur", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 200, "unlocks": ["Match-Sticks Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Sulphur Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 2500, "unlocks": ["Small Nether Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 5000, "unlocks": ["Totem of Corruption Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 10000, "unlocks": ["Medium Nether Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 15000, "unlocks": ["Enchanted Sulphur Cube Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Sulphur Bow Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Large Nether Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 100000, "unlocks": ["Implosion Belt Recipe", "+4 SkyBlock XP"]}]}, "NETHERRACK": {"name": "Netherrack", "maxTiers": 5, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Nether Wart Island Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Wounded Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["Nether Brick Trade", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Netherrack Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Magical Lava Bucket Recipe", "+4 SkyBlock XP"]}]}}}, "COMBAT": {"name": "Combat", "items": {"ENDER_PEARL": {"name": "<PERSON><PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Enderman Minion Recipes", "Silent Pearl Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Enchanted End<PERSON>", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["<PERSON><PERSON> Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Small Dragon Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["<PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Medium Dragon Sack Recipe", "Enchanted Eye of <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 15000, "unlocks": ["Teleport Pad Trade", "Absolute Ender <PERSON>", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Aspect of the End Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Large Dragon Sack Recipe", "Saving <PERSON>pe", "+4 SkyBlock XP"]}]}, "CHILI_PEPPER": {"name": "Chili Pepper", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Re-heated Gummy Polar Bear Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["Sulphuric Coal Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 75, "unlocks": ["Capsaicin Eyedrops Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Entropy Suppressor Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Book (Cayenne IV) Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["Stuffed Chili Pepper Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Jalapeno Book Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["Enchanted Book (Habanero Tactics IV) Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 20000, "unlocks": ["Enchanted Book (Tabasco II) Recipe", "+4 SkyBlock XP"]}]}, "SLIME_BALL": {"name": "Slimeball", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Slime Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Slime Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Knockback Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Knockback Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Enchanted Slimeball Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Punch Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Launch Pad Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Enchanted Slime Block Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Slime Bow Recipe", "+4 SkyBlock XP"]}]}, "MAGMA_CREAM": {"name": "Magma Cream", "maxTiers": 8, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Magma Cube Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Fire Protection Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Nether Biome Stick Recipe", "Enchanted Magma Cream Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Magma Necklace Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Lava Bucket Trade", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["<PERSON>va Talisman Recipe", "Searing Stone Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Vanquished Magma Necklace Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Whipped Magma Cream Recipe", "+4 SkyBlock XP"]}]}, "GHAST_TEAR": {"name": "Ghast Tear", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Ghast Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Giant Killer Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Enchanted Ghast Tear Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Vampirism Exp Discount (-25%)", "<PERSON><PERSON><PERSON> Cloak Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Silver Fang Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Meteor <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["Vanquished Ghast Cloak Recipe", "+4 SkyBlock XP"]}]}, "SULPHUR": {"name": "Gunpowder", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Creeper Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Creeper Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Blast Protection Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Gunpowder Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Thunderlord Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Enchanted Firework Rocket Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["+10,000 Combat Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Creeper Pants Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Explosive Bow Recipe", "+4 SkyBlock XP"]}]}, "ROTTEN_FLESH": {"name": "Rotten Flesh", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Zombie Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Zombie Pickaxe Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Zombie Recipe", "Smite Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Rotten Flesh Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Zombie Hat Recipe", "Small Combat Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Zombie's Heart Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Zombie Sword Recipe", "Medium Combat Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Zombie Chestplate Recipe", "Zombie Leggings Recipe", "Zombie Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Large Combat Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Large Enchanted Combat Sack Recipe", "+4 SkyBlock XP"]}]}, "SPIDER_EYE": {"name": "Spider Eye", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Cave Spider Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Spider Sword Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Spider Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted Spider Eye Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Bane of Arthropods Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Venomous Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Enchanted Fermented Spider Eye Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["+25,000 Combat Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Leaping Sword Recipe", "+4 SkyBlock XP"]}]}, "BONE": {"name": "Bone", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Skeleton Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Enchanted Bone Meal Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Skeleton Recipe", "Power Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Skeleton Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Enchanted Bone Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["+1,000 Combat Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["Hurricane Bow Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["Skeleton's <PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["<PERSON><PERSON>'s Bow Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 150000, "unlocks": ["Enchanted Bone Block Recipe", "+4 SkyBlock XP"]}]}, "BLAZE_ROD": {"name": "<PERSON>", "maxTiers": 8, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Blaze Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Fire Aspect Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Enchanted Blaze Powder Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 2500, "unlocks": ["Fire Talisman Recipe", "Blaze Belt Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Flame Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 10000, "unlocks": ["Enchanted Blaze Rod Recipe", "Blaze Wax Recipe", "[Lvl 1] Blaze Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 25000, "unlocks": ["<PERSON> He<PERSON>et Recipe", "Blaze Chestplate Recipe", "Blaze Leggings Recipe", "Blaze Boots Recipe", "Vanquished Blaze Belt Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["+10,000 Combat Experience", "+4 SkyBlock XP"]}]}, "STRING": {"name": "String", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Spider Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Web Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Spider Recipe", "Quiver", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Enchanted String Recipe", "Grappling <PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 2500, "unlocks": ["Silk Touch Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 5000, "unlocks": ["Infinite Quiver Exp Discount (-25%)", "Large Quiver Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 10000, "unlocks": ["+20,000 Combat Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 25000, "unlocks": ["<PERSON>'s <PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 50000, "unlocks": ["Giant Quiver Upgrade (+9 slots)", "+4 SkyBlock XP"]}]}}}, "FORAGING": {"name": "Foraging", "items": {"SEA_LUMIES": {"name": "Sea Lumies", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 25, "unlocks": ["Basic Fishing Net Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Enchanted Sea Lumies Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Bubbles of Air Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Enchanted Book (Scuba I) Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Medium Fishing Net Recipe", "Pressure Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 750, "unlocks": ["Diver's Mask Recipe", "Diver's <PERSON><PERSON>", "Diver's Trunks Recipe", "Diver's Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 1000, "unlocks": ["Pressure Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 2500, "unlocks": ["Abyssal Helmet Recipe", "Abyssal Chestplate Recipe", "Abyssal Leggings Recipe", "Abyssal Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 5000, "unlocks": ["Turbo Fishing Net Recipe", "Pressure Artifact Recipe", "+4 SkyBlock XP"]}]}, "LOG_2": {"name": "Acacia Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Acacia Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Acacia Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Acacia Log Recipe", "Portal to Savanna Woodland Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Savanna Bow Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Savanna Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["+2,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Repelling Candle Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Acacia Birdhouse Recipe", "+4 SkyBlock XP"]}]}, "LOG:1": {"name": "Spruce Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Spruce Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Spruce Axe Recipe", "Spruce Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Spruce Log Recipe", "[Lvl 1] Wolf Recipe", "Portal to Spruce Woods Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["+500 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Taiga Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["+2,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Woodcutting Crystal Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["+25,000 Foraging Experience", "+4 SkyBlock XP"]}]}, "VINESAP": {"name": "Vinesap", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["<PERSON>orn Huntaxe - Genesis Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["<PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 50, "unlocks": ["Sharpened Huntaxe - <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["Enchanted Vinesap Recipe", "Entan<PERSON>", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 200, "unlocks": ["Reinforced Huntaxe - Cursus Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 400, "unlocks": ["<PERSON><PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 600, "unlocks": ["<PERSON> - <PERSON>rae<PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 800, "unlocks": ["+50,000 Hunting Experience", "+50,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 1000, "unlocks": ["Prime Huntaxe - Nex Titanum Recipe", "+4 SkyBlock XP"]}]}, "LOG:3": {"name": "Jungle Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Jungle Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Jungle Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["[Lvl 1] Ocelot Recipe", "Enchanted Jungle Log Recipe", "Portal to Jungle Island Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Vines Trade", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Jungle Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["+2,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Treecapitator Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}]}, "LOG:2": {"name": "Birch Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Birch Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Birch Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Birch Log Recipe", "Portal to Birch Park Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Sculptor's Axe Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Birch Forest Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["Small Foraging Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Medium Foraging Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["Large Foraging Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Large Enchanted Foraging Sack Recipe", "+4 SkyBlock XP"]}]}, "LUSHLILAC": {"name": "Lu<PERSON><PERSON>lac", "maxTiers": 5, "tiers": [{"tier": 1, "amountRequired": 25, "unlocks": ["Lushlilac Bonbon Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["<PERSON><PERSON> Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Prime <PERSON><PERSON><PERSON><PERSON>c Bonbon Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["+1,000 Hunting Experience", "+1,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Exalted <PERSON><PERSON><PERSON>la<PERSON> Bonbon Recipe", "+4 SkyBlock XP"]}]}, "MANGROVE_LOG": {"name": "Mangrove Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 500, "unlocks": ["Travel Scroll to Murkwater Loch Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Mangrove Log Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 2500, "unlocks": ["Mangrove Locket Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 5000, "unlocks": ["Mangrove Vine Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 10000, "unlocks": ["Mangrove Gem Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["Mangcore Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 50000, "unlocks": ["Seagull Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 100000, "unlocks": ["Mangrove Grippers Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 150000, "unlocks": ["+110,000 Foraging Experience", "+1 Foraging Level Cap", "+4 SkyBlock XP"]}]}, "LOG": {"name": "Oak Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Oak Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Leaflet Hat Recipe", "Leaflet Tunic Recipe", "Leaflet Pants Recipe", "Leaflet Sandals Recipe", "Oak Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Oak Log Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Small Storage Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Forest Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["Medium Storage Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Wood Affinity Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Large Storage Recipe", "+4 SkyBlock XP"]}]}, "FIG_LOG": {"name": "Fig Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 500, "unlocks": ["Tiny Scaffolding Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 1000, "unlocks": ["Enchanted Fig Log Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 2500, "unlocks": ["Medium Scaffolding Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 5000, "unlocks": ["<PERSON>g <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 10000, "unlocks": ["Large Scaffolding Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 25000, "unlocks": ["Figstone Recipe", "Figstone Splitter Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 50000, "unlocks": ["Sparrow Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 100000, "unlocks": ["Fig Cap Recipe", "<PERSON><PERSON><PERSON>", "Fig Trousers Recipe", "Fig Striders Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 150000, "unlocks": ["+100,000 Foraging Experience", "+1 Foraging Level Cap", "+4 SkyBlock XP"]}]}, "TENDER_WOOD": {"name": "Tender Wood", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Small Huntrap Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["Small Fish Bowl Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 50, "unlocks": ["Medium Huntrap Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["Enchanted Tender Wood Recipe", "Medium Fish Bowl Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 200, "unlocks": ["Large Huntrap Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 400, "unlocks": ["Large Fish Bowl Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 600, "unlocks": ["Greater Huntrap Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 800, "unlocks": ["+50,000 Hunting Experience", "+50,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 1000, "unlocks": ["Astral Huntrap Recipe", "+4 SkyBlock XP"]}]}, "LOG_2:1": {"name": "Dark Oak Log", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Dark Oak Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Dark Oak Leaves Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Enchanted Dark Oak Log Recipe", "Portal to Dark Thicket Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["Roofed Forest Island Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1000, "unlocks": ["Roofed Forest Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2000, "unlocks": ["+2,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Growth Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 10000, "unlocks": ["+10,000 Foraging Experience", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 25000, "unlocks": ["Helmet of Growth Recipe", "Chestplate of Growth Recipe", "Leggings of Growth Recipe", "Boots of Growth Recipe", "+4 SkyBlock XP"]}]}}}, "FISHING": {"name": "Fishing", "items": {"WATER_LILY": {"name": "<PERSON>", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Spooky Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Blobfish Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Healing Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 200, "unlocks": ["Enchanted Lily Pad Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Common Hook Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 1500, "unlocks": ["<PERSON><PERSON> Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 3000, "unlocks": ["Rod of Champions Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 6000, "unlocks": ["Healing Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 10000, "unlocks": ["Rod of Legends Recipe", "+4 SkyBlock XP"]}]}, "PRISMARINE_SHARD": {"name": "<PERSON><PERSON><PERSON><PERSON>", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Impaling Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["Prismarine Blade Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 50, "unlocks": ["Enchanted <PERSON><PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["<PERSON><PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 200, "unlocks": ["<PERSON><PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 400, "unlocks": ["Weather Node Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 800, "unlocks": ["<PERSON><PERSON><PERSON><PERSON> Recipe", "+4 SkyBlock XP"]}]}, "INK_SACK": {"name": "Ink Sac", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Squid Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 40, "unlocks": ["Dark Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Enchanted Ink Sac Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 200, "unlocks": ["Deep Ocean Biome Stick Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 400, "unlocks": ["Cast<PERSON> Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 800, "unlocks": ["Blindness Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 1500, "unlocks": ["<PERSON><PERSON> Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 2500, "unlocks": ["Bait Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 4000, "unlocks": ["Ink Wand Recipe", "+4 SkyBlock XP"]}]}, "RAW_FISH": {"name": "Raw Fish", "maxTiers": 11, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Fish Hat Recipe", "Minnow Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Fishing Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Small Fishing Bag", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Pond Island Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["+2,500 Fishing Experience", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 1000, "unlocks": ["Enchanted Raw Fish Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 2500, "unlocks": ["Medium Fishing Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 15000, "unlocks": ["Enchanted Cooked Fish Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 30000, "unlocks": ["Large Fishing Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 45000, "unlocks": ["Giant Fishing Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 60000, "unlocks": ["Massive Fishing Bag Upgrade (+9 slots)", "+4 SkyBlock XP"]}]}, "RAW_FISH:3": {"name": "Pufferfish", "maxTiers": 10, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Pufferfish Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Enchanted Pufferfish Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Cleave Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 150, "unlocks": ["Depth Strider Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 400, "unlocks": ["<PERSON>d <PERSON>t Recipe", "Small Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 800, "unlocks": ["Spiked Hook Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 2400, "unlocks": ["Medium Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 4800, "unlocks": ["Hotspot Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 9000, "unlocks": ["Large Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 18000, "unlocks": ["Large Enchanted Fishing Sack Recipe", "+4 SkyBlock XP"]}]}, "RAW_FISH:2": {"name": "Clownfish", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Clownfish Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["Water Bucket Trade", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 50, "unlocks": ["Magnet Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["Small Sack of Sacks", "Enchanted Clownfish Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 200, "unlocks": ["Medium Sack of Sacks Upgrade (+3 slots)", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 400, "unlocks": ["Large Sack of Sacks Upgrade (+3 slots)", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 800, "unlocks": ["Clownfish Cloak Recipe", "Greater Sack of Sacks Upgrade (+3 slots)", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 1600, "unlocks": ["Giant Sack of Sacks Upgrade (+3 slots)", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 4000, "unlocks": ["Massive Sack of Sacks Upgrade (+3 slots)", "+4 SkyBlock XP"]}]}, "RAW_FISH:1": {"name": "Raw Salmon", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Salmon Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Dodge Potion Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Lure Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Enchanted Raw Salmon Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Fish Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 1000, "unlocks": ["Salmon Helmet Recipe", "Salmon Chestplate Recipe", "Salmon Leggings Recipe", "Salmon Boots Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 2500, "unlocks": ["+5,000 Fishing Experience", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 5000, "unlocks": ["Enchanted Cooked Salmon Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 10000, "unlocks": ["+10,000 Fishing Experience", "+4 SkyBlock XP"]}]}, "MAGMA_FISH": {"name": "Magmafish", "maxTiers": 12, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Magmafish Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Hot Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 500, "unlocks": ["Silver Magmafish Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Magma Rod Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 5000, "unlocks": ["Corrupted Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 15000, "unlocks": ["Small Lava Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 30000, "unlocks": ["Gold Magmafish Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 50000, "unlocks": ["Inferno Rod Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 75000, "unlocks": ["Medium Lava Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 10, "amountRequired": 100000, "unlocks": ["Diamond Magmafish Recipe", "+4 SkyBlock XP"]}, {"tier": 11, "amountRequired": 250000, "unlocks": ["Large Lava Fishing Sack Recipe", "+4 SkyBlock XP"]}, {"tier": 12, "amountRequired": 500000, "unlocks": ["Hellfire Rod Recipe", "+4 SkyBlock XP"]}]}, "PRISMARINE_CRYSTALS": {"name": "Prismarine Crystals", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 10, "unlocks": ["Sea Lantern Hat Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 25, "unlocks": ["Light Bait Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 50, "unlocks": ["Enchanted Prismarine Crystals Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["Aqua Affinity Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 200, "unlocks": ["Guardian Chestplate Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 400, "unlocks": ["<PERSON>", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 800, "unlocks": ["Blessing Exp Discount (-25%)", "+4 SkyBlock XP"]}]}, "CLAY_BALL": {"name": "<PERSON>", "maxTiers": 7, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Clay Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 100, "unlocks": ["Enchanted Clay Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 250, "unlocks": ["Respiration Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 1000, "unlocks": ["Frail Exp Discount (-25%)", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 1500, "unlocks": ["Clay Bracelet Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 2500, "unlocks": ["+2,500 Fishing Experience", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 5000, "unlocks": ["Enchanted Clay Block Recipe", "+4 SkyBlock XP"]}]}, "SPONGE": {"name": "Sponge", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Sponge Trade", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Sponge Sinker Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Sea Creature Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Enchanted Sponge Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Sponge Belt Recipe", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 1000, "unlocks": ["Sea Creature Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 1500, "unlocks": ["Enchanted Wet Sponge Recipe", "Stereo Pants Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 2000, "unlocks": ["Sea Creature Artifact Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 4000, "unlocks": ["Sponge Helmet Recipe", "Sponge Chestplate Recipe", "Sponge Leggings Recipe", "Sponge Boots Recipe", "+4 SkyBlock XP"]}]}}}, "RIFT": {"name": "Rift", "items": {"WILTED_BERBERIS": {"name": "Wilted <PERSON><PERSON><PERSON>", "maxTiers": 4, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Wilted <PERSON><PERSON><PERSON> Bunch Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 60, "unlocks": ["<PERSON><PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 140, "unlocks": ["Jinxed Voodoo Doll Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 400, "unlocks": ["Berberis Fuel Injector Recipe", "+4 SkyBlock XP"]}]}, "METAL_HEART": {"name": "Living Metal Heart", "maxTiers": 4, "tiers": [{"tier": 1, "amountRequired": 1, "unlocks": ["Snake-in-a-Boot Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 20, "unlocks": ["Living Metal Anchor Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 60, "unlocks": ["Bluetooth Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 100, "unlocks": ["Polarvoid Book Recipe", "+4 SkyBlock XP"]}]}, "CADUCOUS_STEM": {"name": "Caducous Stem", "maxTiers": 4, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Caducous Stem Bunch Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 60, "unlocks": ["Caducous Legume Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 150, "unlocks": ["Caducous Extract Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 500, "unlocks": ["<PERSON><PERSON><PERSON><PERSON> Feeder <PERSON>", "+4 SkyBlock XP"]}]}, "AGARICUS_CAP": {"name": "Agaricus Cap", "maxTiers": 4, "tiers": [{"tier": 1, "amountRequired": 20, "unlocks": ["Agaricus Cap Bunch Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 60, "unlocks": ["Agaricus Soup Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Agaricus Cap Cap Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 200, "unlocks": ["Agaricus Chumcap Recipe", "+4 SkyBlock XP"]}]}, "HEMOVIBE": {"name": "Hemovibe", "maxTiers": 9, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Vampire Minion Recipes", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 250, "unlocks": ["Blood Donor Talisman Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Hemoglass Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 5000, "unlocks": ["Enchanted Book (Transylvanian IV) Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 15000, "unlocks": ["Displaced <PERSON><PERSON>", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 30000, "unlocks": ["Blood Donor Ring Recipe", "+4 SkyBlock XP"]}, {"tier": 7, "amountRequired": 75000, "unlocks": ["Full-Jaw Fanging Kit Recipe", "+4 SkyBlock XP"]}, {"tier": 8, "amountRequired": 150000, "unlocks": ["Presumed <PERSON><PERSON><PERSON> of Red Paint Recipe", "+4 SkyBlock XP"]}, {"tier": 9, "amountRequired": 250000, "unlocks": ["Hemobomb Recipe", "Blood Donor Artifact Recipe", "+4 SkyBlock XP"]}]}, "HALF_EATEN_CARROT": {"name": "<PERSON>-<PERSON><PERSON>", "maxTiers": 4, "tiers": [{"tier": 1, "amountRequired": 50, "unlocks": ["Nearly-Whole Carrot Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 400, "unlocks": ["Orange Chestplate Recipe", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 1000, "unlocks": ["Exportable Carrots Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 3500, "unlocks": ["Nearly Coherent doR gnihsiF Recipe", "+4 SkyBlock XP"]}]}, "TIMITE": {"name": "Timite", "maxTiers": 6, "tiers": [{"tier": 1, "amountRequired": 25, "unlocks": ["Time Gun Recipe", "<PERSON><PERSON> Pickaxe Recipe", "+4 SkyBlock XP"]}, {"tier": 2, "amountRequired": 50, "unlocks": ["Time Pocket Bag", "+4 SkyBlock XP"]}, {"tier": 3, "amountRequired": 100, "unlocks": ["Chrono Pickaxe Recipe", "+4 SkyBlock XP"]}, {"tier": 4, "amountRequired": 250, "unlocks": ["Highlite Recipe", "+4 SkyBlock XP"]}, {"tier": 5, "amountRequired": 500, "unlocks": ["Satelite Reci<PERSON>", "+4 SkyBlock XP"]}, {"tier": 6, "amountRequired": 750, "unlocks": ["Discrite Recipe", "+4 SkyBlock XP"]}]}}}}}