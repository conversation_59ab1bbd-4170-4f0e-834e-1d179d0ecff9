<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkyBlock 3D Adventure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            overflow: hidden;
            color: white;
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        #game-canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .ui-overlay > * {
            pointer-events: auto;
        }

        .top-hud {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 1001;
        }

        .player-stats {
            display: flex;
            gap: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 10px 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            backdrop-filter: blur(10px);
        }

        .stat-icon {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #ffd700;
        }

        .stat-label {
            font-size: 0.8em;
            color: #ccc;
        }

        .game-title h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            color: #ffd700;
        }

        .interaction-prompt {
            position: absolute;
            bottom: 50%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #ffd700;
            border-radius: 15px;
            padding: 15px 25px;
            display: none;
            backdrop-filter: blur(10px);
        }

        .prompt-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .prompt-icon {
            font-size: 2em;
        }

        .prompt-text {
            font-size: 1.2em;
            font-weight: bold;
            color: #ffd700;
        }

        .controls-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .controls-info h4 {
            color: #ffd700;
            margin-bottom: 10px;
        }

        .control-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            min-width: 200px;
        }

        .key {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #ffd700;
        }

        .desc {
            color: #ccc;
        }

        .activity-log {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            max-height: 200px;
            overflow-y: auto;
        }

        .activity-log h4 {
            color: #ffd700;
            margin-bottom: 10px;
        }

        .log-entry {
            padding: 3px 0;
            font-size: 0.8em;
            color: #ccc;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry:last-child {
            border-bottom: none;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div class="game-container">
        <canvas id="game-canvas"></canvas>

        <div class="ui-overlay">
            <!-- Top HUD -->
            <div class="top-hud">
                <div class="player-stats">
                    <div class="stat-item">
                        <span class="stat-icon">💰</span>
                        <span class="stat-value" id="coins">0</span>
                        <span class="stat-label">Coins</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">❤️</span>
                        <span class="stat-value" id="health">100</span>
                        <span class="stat-label">Health</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">⭐</span>
                        <span class="stat-value" id="skyblock-xp">0</span>
                        <span class="stat-label">SkyBlock XP</span>
                    </div>
                </div>
                
                <div class="game-title">
                    <h1>🏝️ SkyBlock 3D</h1>
                </div>
            </div>

            <!-- Interaction Prompt -->
            <div class="interaction-prompt" id="interaction-prompt">
                <div class="prompt-content">
                    <div class="prompt-icon" id="prompt-icon">🌾</div>
                    <div class="prompt-text" id="prompt-text">Stiskni E pro farmařství</div>
                </div>
            </div>

            <!-- Controls Info -->
            <div class="controls-info">
                <h4>🎮 Ovládání</h4>
                <div class="control-item">
                    <span class="key">WASD</span>
                    <span class="desc">Pohyb postavy</span>
                </div>
                <div class="control-item">
                    <span class="key">Shift</span>
                    <span class="desc">Běh</span>
                </div>
                <div class="control-item">
                    <span class="key">E</span>
                    <span class="desc">Interakce</span>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="activity-log">
                <h4>📜 Log aktivit</h4>
                <div id="log-entries">
                    <div class="log-entry">Vítej v SkyBlock 3D Adventure! 🎮</div>
                    <div class="log-entry">Použij WASD pro pohyb postavy 🚶</div>
                    <div class="log-entry">Přiblíž se k různým oblastem a stiskni E ⚡</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple 3D Game
        class Simple3DGame {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.player = null;
                this.clock = new THREE.Clock();
                
                this.playerSpeed = 5;
                this.keys = { w: false, a: false, s: false, d: false, shift: false };
                this.cameraOffset = new THREE.Vector3(0, 8, 10);
                
                this.interactiveAreas = [];
                this.currentInteraction = null;
                
                this.playerData = {
                    coins: 0,
                    health: 100,
                    skyblockXP: 0,
                    farmingXP: 0,
                    miningXP: 0,
                    combatXP: 0
                };
            }

            init() {
                this.createScene();
                this.createCamera();
                this.createRenderer();
                this.createPlayer();
                this.createWorld();
                this.createInteractiveAreas();
                this.setupEventListeners();
                this.animate();
            }

            createScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB);
                this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
            }

            createCamera() {
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.camera.position.set(0, 8, 10);
            }

            createRenderer() {
                const canvas = document.getElementById('game-canvas');
                this.renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            }

            createPlayer() {
                const playerGroup = new THREE.Group();
                
                // Body
                const bodyGeometry = new THREE.CapsuleGeometry(0.5, 1.5, 4, 8);
                const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
                const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
                body.position.y = 1;
                body.castShadow = true;
                playerGroup.add(body);
                
                // Head
                const headGeometry = new THREE.SphereGeometry(0.4, 8, 6);
                const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB5 });
                const head = new THREE.Mesh(headGeometry, headMaterial);
                head.position.y = 2.2;
                head.castShadow = true;
                playerGroup.add(head);
                
                playerGroup.position.set(0, 0, 0);
                this.scene.add(playerGroup);
                this.player = playerGroup;
                
                this.player.userData = { body: body, head: head, walkCycle: 0 };
            }

            createWorld() {
                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
                this.scene.add(ambientLight);

                const sunLight = new THREE.DirectionalLight(0xffffff, 0.8);
                sunLight.position.set(50, 50, 50);
                sunLight.castShadow = true;
                this.scene.add(sunLight);

                // Main island
                const islandGeometry = new THREE.CylinderGeometry(25, 30, 5, 32);
                const islandMaterial = new THREE.MeshLambertMaterial({ color: 0x8FBC8F });
                const island = new THREE.Mesh(islandGeometry, islandMaterial);
                island.position.y = -2.5;
                island.receiveShadow = true;
                this.scene.add(island);

                // Grass
                const grassGeometry = new THREE.CylinderGeometry(25.1, 30.1, 0.2, 32);
                const grassMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
                const grass = new THREE.Mesh(grassGeometry, grassMaterial);
                grass.position.y = 0.1;
                grass.receiveShadow = true;
                this.scene.add(grass);

                // Farm area
                this.createFarmArea();
                this.createMineArea();
                this.createCombatArea();
            }

            createFarmArea() {
                for (let i = 0; i < 6; i++) {
                    const plotGeometry = new THREE.BoxGeometry(3, 0.2, 3);
                    const plotMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
                    const plot = new THREE.Mesh(plotGeometry, plotMaterial);
                    plot.position.set(-15 + (i % 3) * 4, 0.2, -10 + Math.floor(i / 3) * 4);
                    plot.receiveShadow = true;
                    this.scene.add(plot);
                }
            }

            createMineArea() {
                const mineGeometry = new THREE.BoxGeometry(4, 3, 2);
                const mineMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
                const mine = new THREE.Mesh(mineGeometry, mineMaterial);
                mine.position.set(15, 1.5, -5);
                mine.castShadow = true;
                this.scene.add(mine);
            }

            createCombatArea() {
                const arenaGeometry = new THREE.CylinderGeometry(8, 8, 0.5, 16);
                const arenaMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
                const arena = new THREE.Mesh(arenaGeometry, arenaMaterial);
                arena.position.set(-15, 0.25, 15);
                arena.receiveShadow = true;
                this.scene.add(arena);
            }

            createInteractiveAreas() {
                this.interactiveAreas = [
                    { position: new THREE.Vector3(-15, 0, -8), radius: 8, skill: 'FARMING', icon: '🌾', name: 'Farmařit' },
                    { position: new THREE.Vector3(15, 0, -5), radius: 6, skill: 'MINING', icon: '⛏️', name: 'Těžit' },
                    { position: new THREE.Vector3(-15, 0, 15), radius: 6, skill: 'COMBAT', icon: '⚔️', name: 'Bojovat' }
                ];
            }

            setupEventListeners() {
                window.addEventListener('resize', () => this.onWindowResize());
                window.addEventListener('keydown', (event) => this.onKeyDown(event));
                window.addEventListener('keyup', (event) => this.onKeyUp(event));
            }

            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }

            onKeyDown(event) {
                switch (event.code) {
                    case 'KeyW': this.keys.w = true; break;
                    case 'KeyA': this.keys.a = true; break;
                    case 'KeyS': this.keys.s = true; break;
                    case 'KeyD': this.keys.d = true; break;
                    case 'ShiftLeft':
                    case 'ShiftRight': this.keys.shift = true; break;
                    case 'KeyE':
                        if (this.currentInteraction) {
                            this.performInteraction();
                        }
                        break;
                }
            }

            onKeyUp(event) {
                switch (event.code) {
                    case 'KeyW': this.keys.w = false; break;
                    case 'KeyA': this.keys.a = false; break;
                    case 'KeyS': this.keys.s = false; break;
                    case 'KeyD': this.keys.d = false; break;
                    case 'ShiftLeft':
                    case 'ShiftRight': this.keys.shift = false; break;
                }
            }

            updatePlayer(deltaTime) {
                if (!this.player) return;
                
                const direction = new THREE.Vector3();
                
                if (this.keys.w) direction.z -= 1;
                if (this.keys.s) direction.z += 1;
                if (this.keys.a) direction.x -= 1;
                if (this.keys.d) direction.x += 1;
                
                if (direction.length() > 0) {
                    direction.normalize();
                    const speed = this.keys.shift ? this.playerSpeed * 2 : this.playerSpeed;
                    direction.multiplyScalar(speed * deltaTime);
                    
                    this.player.position.add(direction);
                    
                    if (direction.length() > 0) {
                        const angle = Math.atan2(direction.x, direction.z);
                        this.player.rotation.y = angle;
                    }
                    
                    // Keep player on island
                    const distanceFromCenter = this.player.position.length();
                    if (distanceFromCenter > 25) {
                        this.player.position.normalize().multiplyScalar(25);
                    }
                    
                    this.player.position.y = Math.max(0, this.player.position.y);
                }
            }

            updateCamera() {
                if (!this.player) return;
                
                const idealOffset = this.cameraOffset.clone();
                idealOffset.applyQuaternion(this.player.quaternion);
                const idealPosition = this.player.position.clone().add(idealOffset);
                
                this.camera.position.lerp(idealPosition, 0.1);
                
                const cameraTarget = this.player.position.clone();
                cameraTarget.y += 1.5;
                this.camera.lookAt(cameraTarget);
            }

            checkInteractions() {
                if (!this.player) return;
                
                let nearestInteraction = null;
                let nearestDistance = Infinity;
                
                this.interactiveAreas.forEach(area => {
                    const distance = this.player.position.distanceTo(area.position);
                    if (distance < area.radius && distance < nearestDistance) {
                        nearestDistance = distance;
                        nearestInteraction = area;
                    }
                });
                
                if (nearestInteraction !== this.currentInteraction) {
                    this.currentInteraction = nearestInteraction;
                    this.updateInteractionPrompt();
                }
            }

            updateInteractionPrompt() {
                const prompt = document.getElementById('interaction-prompt');
                const icon = document.getElementById('prompt-icon');
                const text = document.getElementById('prompt-text');
                
                if (this.currentInteraction) {
                    icon.textContent = this.currentInteraction.icon;
                    text.textContent = `Stiskni E pro ${this.currentInteraction.name}`;
                    prompt.style.display = 'block';
                } else {
                    prompt.style.display = 'none';
                }
            }

            performInteraction() {
                if (!this.currentInteraction) return;
                
                const skill = this.currentInteraction.skill;
                const xpGain = 10 + Math.floor(Math.random() * 15);
                
                // Add XP
                if (skill === 'FARMING') this.playerData.farmingXP += xpGain;
                else if (skill === 'MINING') this.playerData.miningXP += xpGain;
                else if (skill === 'COMBAT') this.playerData.combatXP += xpGain;
                
                // Random coins
                const coinsGain = Math.floor(Math.random() * 10) + 1;
                this.playerData.coins += coinsGain;
                
                this.addLogEntry(`+${xpGain} ${skill} XP získáno! 💪`);
                this.addLogEntry(`+${coinsGain} coins! 💰`);
                
                this.updateUI();
            }

            updateUI() {
                document.getElementById('coins').textContent = this.playerData.coins;
                document.getElementById('health').textContent = this.playerData.health;
                document.getElementById('skyblock-xp').textContent = this.playerData.skyblockXP;
            }

            addLogEntry(message) {
                const logEntries = document.getElementById('log-entries');
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                
                logEntries.insertBefore(logEntry, logEntries.firstChild);
                
                while (logEntries.children.length > 10) {
                    logEntries.removeChild(logEntries.lastChild);
                }
            }

            animate() {
                requestAnimationFrame(() => this.animate());
                
                const deltaTime = this.clock.getDelta();
                
                this.updatePlayer(deltaTime);
                this.updateCamera();
                this.checkInteractions();
                
                this.renderer.render(this.scene, this.camera);
            }
        }

        // Start the game
        const game = new Simple3DGame();
        game.init();
    </script>
</body>
</html>
