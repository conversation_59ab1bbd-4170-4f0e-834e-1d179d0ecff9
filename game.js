// SkyBlock Adventure Game Logic
class SkyBlockGame {
    constructor() {
        this.playerData = {
            coins: 0,
            health: 100,
            skyblockXP: 0,
            skills: {},
            inventory: {},
            collections: {}
        };
        
        this.currentCollectionTab = 'FARMING';
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) return;
        
        // Load all game data
        const success = await window.dataLoader.loadAllData();
        if (!success) {
            console.error('Failed to load game data');
            return;
        }

        // Initialize player skills
        const skills = window.dataLoader.getAllSkills();
        for (const skillKey of Object.keys(skills)) {
            if (!this.playerData.skills[skillKey]) {
                this.playerData.skills[skillKey] = {
                    xp: 0,
                    level: 0
                };
            }
        }

        // Initialize collections
        const collections = window.dataLoader.getAllCollections();
        for (const [categoryKey, categoryData] of Object.entries(collections)) {
            if (!this.playerData.collections[categoryKey]) {
                this.playerData.collections[categoryKey] = {};
            }
            
            if (categoryData.items) {
                for (const itemKey of Object.keys(categoryData.items)) {
                    if (!this.playerData.collections[categoryKey][itemKey]) {
                        this.playerData.collections[categoryKey][itemKey] = {
                            collected: 0,
                            tier: 0
                        };
                    }
                }
            }
        }

        this.isInitialized = true;

        // Initialize world map
        if (window.worldMap) {
            window.worldMap.initialize();
        }

        this.updateUI();
        this.addLogEntry('Hra byla úspěšně načtena! 🎉');
    }

    performAction(skillKey) {
        if (!this.isInitialized) return;

        const baseXP = 10 + Math.floor(Math.random() * 15); // 10-24 XP
        const oldLevel = this.playerData.skills[skillKey].level;
        
        // Add XP
        this.playerData.skills[skillKey].xp += baseXP;
        
        // Check for level up
        const newLevel = window.dataLoader.getLevelFromXP(skillKey, this.playerData.skills[skillKey].xp);
        if (newLevel > oldLevel) {
            this.levelUp(skillKey, oldLevel, newLevel);

            // Check for newly unlocked locations
            if (window.worldMap) {
                window.worldMap.checkUnlockedLocations();
            }
        }

        this.playerData.skills[skillKey].level = newLevel;

        // Chance to get item (30% chance)
        if (Math.random() < 0.3) {
            const item = window.dataLoader.getRandomActivityItem(skillKey);
            if (item) {
                this.addItemToInventory(item);
            }
        }

        this.addLogEntry(`+${baseXP} ${skillKey} XP získáno! 💪`);
        this.updateUI();
    }

    levelUp(skillKey, oldLevel, newLevel) {
        for (let level = oldLevel + 1; level <= newLevel; level++) {
            const unlocks = window.dataLoader.getUnlocksForLevel(skillKey, level);
            
            // Process unlocks
            unlocks.forEach(unlock => {
                if (unlock.includes('Coins')) {
                    const coins = parseInt(unlock.match(/\+?([\d,]+)\s*Coins/)?.[1]?.replace(/,/g, '') || 0);
                    this.playerData.coins += coins;
                }
                
                if (unlock.includes('Health')) {
                    const health = parseInt(unlock.match(/\+?(\d+)\s*❤?\s*Health/)?.[1] || 0);
                    this.playerData.health += health;
                }
                
                if (unlock.includes('SkyBlock XP')) {
                    const xp = parseInt(unlock.match(/\+?(\d+)\s*SkyBlock XP/)?.[1] || 0);
                    this.playerData.skyblockXP += xp;
                }
            });
            
            this.addLogEntry(`🎉 ${skillKey} Level ${level} dosažen!`);
            
            if (unlocks.length > 0) {
                this.addLogEntry(`🎁 Odměny: ${unlocks.slice(0, 3).join(', ')}${unlocks.length > 3 ? '...' : ''}`);
            }
        }
    }

    addItemToInventory(item) {
        const itemId = item.id;
        if (!this.playerData.inventory[itemId]) {
            this.playerData.inventory[itemId] = {
                item: item,
                count: 0
            };
        }
        
        this.playerData.inventory[itemId].count++;
        this.addLogEntry(`📦 Získán předmět: ${item.name} (${item.tier})`);
        
        // Update collections if applicable
        this.updateCollections(item);
    }

    updateCollections(item) {
        // Find which collection this item belongs to
        const collections = window.dataLoader.getAllCollections();
        
        for (const [categoryKey, categoryData] of Object.entries(collections)) {
            if (categoryData.items) {
                for (const [itemKey, itemData] of Object.entries(categoryData.items)) {
                    // Simple matching by name or ID
                    if (itemKey.includes(item.material) || item.name.toLowerCase().includes(itemData.name.toLowerCase())) {
                        if (!this.playerData.collections[categoryKey]) {
                            this.playerData.collections[categoryKey] = {};
                        }
                        
                        if (!this.playerData.collections[categoryKey][itemKey]) {
                            this.playerData.collections[categoryKey][itemKey] = {
                                collected: 0,
                                tier: 0
                            };
                        }
                        
                        this.playerData.collections[categoryKey][itemKey].collected++;
                        
                        // Check for tier progression
                        const collectionItem = this.playerData.collections[categoryKey][itemKey];
                        const currentTier = collectionItem.tier;
                        const newTier = this.calculateCollectionTier(itemData, collectionItem.collected);
                        
                        if (newTier > currentTier) {
                            collectionItem.tier = newTier;
                            this.addLogEntry(`🏆 ${itemData.name} kolekce tier ${newTier} dosažen!`);
                        }
                        
                        return;
                    }
                }
            }
        }
    }

    calculateCollectionTier(itemData, collected) {
        if (!itemData.tiers) return 0;
        
        let tier = 0;
        for (const tierData of itemData.tiers) {
            if (collected >= tierData.amountRequired) {
                tier = tierData.tier;
            } else {
                break;
            }
        }
        
        return tier;
    }

    sellAllItems() {
        let totalCoins = 0;
        let itemCount = 0;
        
        for (const [itemId, inventoryItem] of Object.entries(this.playerData.inventory)) {
            const sellPrice = inventoryItem.item.npc_sell_price || 1;
            const coins = sellPrice * inventoryItem.count;
            totalCoins += coins;
            itemCount += inventoryItem.count;
        }
        
        this.playerData.inventory = {};
        this.playerData.coins += totalCoins;
        
        if (itemCount > 0) {
            this.addLogEntry(`💰 Prodáno ${itemCount} předmětů za ${totalCoins} coins!`);
        } else {
            this.addLogEntry('❌ Žádné předměty k prodeji!');
        }
        
        this.updateUI();
    }

    showCollectionTab(categoryKey) {
        this.currentCollectionTab = categoryKey;
        this.updateCollectionsUI();
    }

    updateUI() {
        this.updatePlayerStats();
        this.updateSkillsUI();
        this.updateInventoryUI();
        this.updateCollectionsUI();
    }

    updatePlayerStats() {
        document.getElementById('coins').textContent = this.playerData.coins.toLocaleString();
        document.getElementById('health').textContent = this.playerData.health;
        document.getElementById('skyblock-xp').textContent = this.playerData.skyblockXP.toLocaleString();
    }

    updateSkillsUI() {
        const skillsGrid = document.getElementById('skills-grid');
        skillsGrid.innerHTML = '';
        
        const skills = window.dataLoader.getAllSkills();
        
        for (const [skillKey, skillData] of Object.entries(skills)) {
            const playerSkill = this.playerData.skills[skillKey];
            const currentXP = playerSkill.xp;
            const currentLevel = playerSkill.level;
            const nextLevelXP = window.dataLoader.getXPForLevel(skillKey, currentLevel + 1);
            const currentLevelXP = window.dataLoader.getXPForLevel(skillKey, currentLevel);
            
            const progressPercent = nextLevelXP > 0 ? 
                ((currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100 : 100;
            
            const skillCard = document.createElement('div');
            skillCard.className = 'skill-card';
            skillCard.innerHTML = `
                <div class="skill-header">
                    <span class="skill-name">${skillData.name}</span>
                    <span class="skill-level">Level ${currentLevel}</span>
                </div>
                <div class="skill-progress">
                    <div class="skill-progress-bar" style="width: ${Math.min(progressPercent, 100)}%"></div>
                </div>
                <div class="skill-xp">${currentXP.toLocaleString()} / ${nextLevelXP > 0 ? nextLevelXP.toLocaleString() : 'MAX'} XP</div>
            `;
            
            skillsGrid.appendChild(skillCard);
        }
    }

    updateInventoryUI() {
        const inventoryGrid = document.getElementById('inventory-grid');
        const inventoryCount = document.getElementById('inventory-count');
        
        inventoryGrid.innerHTML = '';
        
        let totalItems = 0;
        
        for (const [itemId, inventoryItem] of Object.entries(this.playerData.inventory)) {
            totalItems += inventoryItem.count;
            
            const itemDiv = document.createElement('div');
            itemDiv.className = 'inventory-item';
            itemDiv.innerHTML = `
                <div class="item-name">${inventoryItem.item.name}</div>
                <div class="item-tier tier-${inventoryItem.item.tier}">${inventoryItem.item.tier}</div>
                <div class="item-count">x${inventoryItem.count}</div>
            `;
            
            inventoryGrid.appendChild(itemDiv);
        }
        
        inventoryCount.textContent = totalItems;
    }

    updateCollectionsUI() {
        const collectionsContent = document.getElementById('collections-content');
        collectionsContent.innerHTML = '';
        
        const categoryData = window.dataLoader.getCollectionData(this.currentCollectionTab);
        if (!categoryData || !categoryData.items) return;
        
        for (const [itemKey, itemData] of Object.entries(categoryData.items)) {
            const playerCollection = this.playerData.collections[this.currentCollectionTab]?.[itemKey] || {
                collected: 0,
                tier: 0
            };
            
            const currentTier = playerCollection.tier;
            const nextTierData = itemData.tiers ? itemData.tiers.find(t => t.tier === currentTier + 1) : null;
            const progressPercent = nextTierData ? 
                (playerCollection.collected / nextTierData.amountRequired) * 100 : 100;
            
            const collectionDiv = document.createElement('div');
            collectionDiv.className = 'collection-item';
            collectionDiv.innerHTML = `
                <div class="collection-header">
                    <span class="collection-name">${itemData.name}</span>
                    <span class="collection-tier">Tier ${currentTier}/${itemData.maxTiers || 0}</span>
                </div>
                <div class="collection-progress">
                    <div class="collection-progress-bar" style="width: ${Math.min(progressPercent, 100)}%"></div>
                </div>
                <div class="collection-count">
                    ${playerCollection.collected.toLocaleString()} / ${nextTierData ? nextTierData.amountRequired.toLocaleString() : 'MAX'}
                </div>
            `;
            
            collectionsContent.appendChild(collectionDiv);
        }
    }

    addLogEntry(message) {
        const activityLog = document.getElementById('activity-log');
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        
        activityLog.insertBefore(logEntry, activityLog.firstChild);
        
        // Keep only last 50 entries
        while (activityLog.children.length > 50) {
            activityLog.removeChild(activityLog.lastChild);
        }
    }

    saveGame() {
        try {
            localStorage.setItem('skyblock-adventure-save', JSON.stringify(this.playerData));
            this.addLogEntry('💾 Hra byla uložena!');
        } catch (error) {
            console.error('Error saving game:', error);
            this.addLogEntry('❌ Chyba při ukládání hry!');
        }
    }

    loadGame() {
        try {
            const saveData = localStorage.getItem('skyblock-adventure-save');
            if (saveData) {
                this.playerData = JSON.parse(saveData);
                this.updateUI();
                this.addLogEntry('📁 Hra byla načtena!');
            } else {
                this.addLogEntry('❌ Žádný uložený soubor nenalezen!');
            }
        } catch (error) {
            console.error('Error loading game:', error);
            this.addLogEntry('❌ Chyba při načítání hry!');
        }
    }

    resetGame() {
        if (confirm('Opravdu chcete resetovat hru? Všechen pokrok bude ztracen!')) {
            localStorage.removeItem('skyblock-adventure-save');
            location.reload();
        }
    }
}

// Global game instance
window.game = new SkyBlockGame();

// Global functions for HTML onclick events
function performAction(skillKey) {
    window.game.performAction(skillKey);
}

function sellAllItems() {
    window.game.sellAllItems();
}

function showCollectionTab(categoryKey) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.textContent.includes(getSkillIcon(categoryKey))) {
            btn.classList.add('active');
        }
    });

    window.game.showCollectionTab(categoryKey);
}

function getSkillIcon(skillKey) {
    const icons = {
        'FARMING': '🌾',
        'MINING': '⛏️',
        'COMBAT': '⚔️',
        'FORAGING': '🪓',
        'FISHING': '🎣'
    };
    return icons[skillKey] || '📚';
}

function saveGame() {
    window.game.saveGame();
}

function loadGame() {
    window.game.loadGame();
}

function resetGame() {
    window.game.resetGame();
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game.initialize();
});
