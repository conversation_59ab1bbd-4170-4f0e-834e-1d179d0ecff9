// 3D World Manager for SkyBlock 3D Adventure
class World3D {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.clock = new THREE.Clock();
        
        this.interactiveObjects = [];
        this.currentInteraction = null;
        
        // Game time
        this.gameTime = 12.0; // 12:00
        this.timeSpeed = 0.01; // How fast time passes
        
        // Weather
        this.weather = 'sunny';
        this.weatherEffects = null;
    }

    async initialize() {
        this.createScene();
        this.createCamera();
        this.createRenderer();
        this.createControls();
        this.createLighting();
        this.createWorld();
        this.createInteractiveObjects();
        this.setupEventListeners();
        
        this.animate();
        
        return true;
    }

    createScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }

    createCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 20);
    }

    createRenderer() {
        const canvas = document.getElementById('game-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }

    createControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.maxPolarAngle = Math.PI / 2.2; // Prevent going underground
        this.controls.minDistance = 5;
        this.controls.maxDistance = 50;
    }

    createLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        this.sunLight = new THREE.DirectionalLight(0xffffff, 0.8);
        this.sunLight.position.set(50, 50, 50);
        this.sunLight.castShadow = true;
        this.sunLight.shadow.mapSize.width = 2048;
        this.sunLight.shadow.mapSize.height = 2048;
        this.sunLight.shadow.camera.near = 0.5;
        this.sunLight.shadow.camera.far = 500;
        this.sunLight.shadow.camera.left = -50;
        this.sunLight.shadow.camera.right = 50;
        this.sunLight.shadow.camera.top = 50;
        this.sunLight.shadow.camera.bottom = -50;
        this.scene.add(this.sunLight);
    }

    createWorld() {
        // Create main island
        this.createMainIsland();
        
        // Create different biomes
        this.createFarmArea();
        this.createMineArea();
        this.createForestArea();
        this.createOceanArea();
        this.createCombatArea();
        
        // Create decorative elements
        this.createClouds();
        this.createWater();
    }

    createMainIsland() {
        // Main island terrain
        const islandGeometry = new THREE.CylinderGeometry(25, 30, 5, 32);
        const islandMaterial = new THREE.MeshLambertMaterial({ color: 0x8FBC8F });
        const island = new THREE.Mesh(islandGeometry, islandMaterial);
        island.position.y = -2.5;
        island.receiveShadow = true;
        this.scene.add(island);

        // Grass on top
        const grassGeometry = new THREE.CylinderGeometry(25.1, 30.1, 0.2, 32);
        const grassMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
        const grass = new THREE.Mesh(grassGeometry, grassMaterial);
        grass.position.y = 0.1;
        grass.receiveShadow = true;
        this.scene.add(grass);
    }

    createFarmArea() {
        // Farm plots
        for (let i = 0; i < 6; i++) {
            const plotGeometry = new THREE.BoxGeometry(3, 0.2, 3);
            const plotMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const plot = new THREE.Mesh(plotGeometry, plotMaterial);
            plot.position.set(-15 + (i % 3) * 4, 0.2, -10 + Math.floor(i / 3) * 4);
            plot.receiveShadow = true;
            this.scene.add(plot);

            // Crops
            if (Math.random() > 0.3) {
                const cropGeometry = new THREE.BoxGeometry(0.5, 1, 0.5);
                const cropMaterial = new THREE.MeshLambertMaterial({ color: 0x32CD32 });
                const crop = new THREE.Mesh(cropGeometry, cropMaterial);
                crop.position.set(
                    plot.position.x + (Math.random() - 0.5) * 2,
                    0.7,
                    plot.position.z + (Math.random() - 0.5) * 2
                );
                crop.castShadow = true;
                this.scene.add(crop);
            }
        }
    }

    createMineArea() {
        // Mine entrance
        const mineGeometry = new THREE.BoxGeometry(4, 3, 2);
        const mineMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const mine = new THREE.Mesh(mineGeometry, mineMaterial);
        mine.position.set(15, 1.5, -5);
        mine.castShadow = true;
        this.scene.add(mine);

        // Mine entrance hole
        const holeGeometry = new THREE.BoxGeometry(2, 2, 2.1);
        const holeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
        const hole = new THREE.Mesh(holeGeometry, holeMaterial);
        hole.position.set(15, 1, -5);
        this.scene.add(hole);

        // Ore deposits
        for (let i = 0; i < 8; i++) {
            const oreGeometry = new THREE.BoxGeometry(0.8, 0.8, 0.8);
            const oreColors = [0x4169E1, 0xFFD700, 0xC0C0C0, 0x8B0000];
            const oreMaterial = new THREE.MeshLambertMaterial({ 
                color: oreColors[Math.floor(Math.random() * oreColors.length)] 
            });
            const ore = new THREE.Mesh(oreGeometry, oreMaterial);
            ore.position.set(
                12 + Math.random() * 6,
                0.4,
                -8 + Math.random() * 6
            );
            ore.castShadow = true;
            this.scene.add(ore);
        }
    }

    createForestArea() {
        // Trees
        for (let i = 0; i < 12; i++) {
            this.createTree(
                -20 + Math.random() * 10,
                0,
                5 + Math.random() * 10
            );
        }
    }

    createTree(x, y, z) {
        // Tree trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.4, 3);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.set(x, y + 1.5, z);
        trunk.castShadow = true;
        this.scene.add(trunk);

        // Tree leaves
        const leavesGeometry = new THREE.SphereGeometry(2, 8, 6);
        const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
        leaves.position.set(x, y + 4, z);
        leaves.castShadow = true;
        this.scene.add(leaves);
    }

    createOceanArea() {
        // Water around island
        const waterGeometry = new THREE.PlaneGeometry(200, 200);
        const waterMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x006994,
            transparent: true,
            opacity: 0.8
        });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.rotation.x = -Math.PI / 2;
        water.position.y = -5;
        water.receiveShadow = true;
        this.scene.add(water);

        // Fishing dock
        const dockGeometry = new THREE.BoxGeometry(8, 0.5, 2);
        const dockMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const dock = new THREE.Mesh(dockGeometry, dockMaterial);
        dock.position.set(0, 0.25, 25);
        dock.castShadow = true;
        this.scene.add(dock);
    }

    createCombatArea() {
        // Combat arena
        const arenaGeometry = new THREE.CylinderGeometry(8, 8, 0.5, 16);
        const arenaMaterial = new THREE.MeshLambertMaterial({ color: 0x8B0000 });
        const arena = new THREE.Mesh(arenaGeometry, arenaMaterial);
        arena.position.set(-15, 0.25, 15);
        arena.receiveShadow = true;
        this.scene.add(arena);

        // Combat dummies
        for (let i = 0; i < 3; i++) {
            const dummyGeometry = new THREE.BoxGeometry(1, 2, 0.5);
            const dummyMaterial = new THREE.MeshLambertMaterial({ color: 0x654321 });
            const dummy = new THREE.Mesh(dummyGeometry, dummyMaterial);
            dummy.position.set(
                -15 + (i - 1) * 3,
                1,
                15
            );
            dummy.castShadow = true;
            this.scene.add(dummy);
        }
    }

    createClouds() {
        for (let i = 0; i < 8; i++) {
            const cloudGeometry = new THREE.SphereGeometry(3, 8, 6);
            const cloudMaterial = new THREE.MeshLambertMaterial({ 
                color: 0xFFFFFF,
                transparent: true,
                opacity: 0.8
            });
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);
            cloud.position.set(
                (Math.random() - 0.5) * 100,
                20 + Math.random() * 10,
                (Math.random() - 0.5) * 100
            );
            this.scene.add(cloud);
        }
    }

    createWater() {
        // Animated water effect will be added in animate loop
    }

    createInteractiveObjects() {
        // Farm interaction area
        this.addInteractiveArea(-15, 0, -8, 8, 'FARMING', '🌾', 'Farmařit');
        
        // Mine interaction area
        this.addInteractiveArea(15, 0, -5, 6, 'MINING', '⛏️', 'Těžit');
        
        // Forest interaction area
        this.addInteractiveArea(-15, 0, 10, 8, 'FORAGING', '🪓', 'Kácet');
        
        // Ocean interaction area
        this.addInteractiveArea(0, 0, 25, 6, 'FISHING', '🎣', 'Rybařit');
        
        // Combat interaction area
        this.addInteractiveArea(-15, 0, 15, 6, 'COMBAT', '⚔️', 'Bojovat');
    }

    addInteractiveArea(x, y, z, radius, skill, icon, name) {
        const geometry = new THREE.CylinderGeometry(radius, radius, 0.1, 16);
        const material = new THREE.MeshLambertMaterial({ 
            color: 0xFFD700,
            transparent: true,
            opacity: 0.3
        });
        const area = new THREE.Mesh(geometry, material);
        area.position.set(x, y + 0.05, z);
        area.userData = { skill, icon, name, radius };
        area.visible = false; // Hidden by default
        this.scene.add(area);
        this.interactiveObjects.push(area);
    }

    setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('keydown', (event) => this.onKeyDown(event));
        
        // Mouse interaction
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        window.addEventListener('mousemove', (event) => this.onMouseMove(event));
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    onKeyDown(event) {
        switch (event.code) {
            case 'KeyE':
                if (this.currentInteraction) {
                    this.performInteraction();
                }
                break;
            case 'KeyH':
                this.toggleControls();
                break;
            case 'Tab':
                event.preventDefault();
                this.toggleInventory();
                break;
        }
    }

    onMouseMove(event) {
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        this.checkInteractions();
    }

    checkInteractions() {
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // Check distance to interactive areas
        const cameraPosition = this.camera.position;
        let nearestInteraction = null;
        let nearestDistance = Infinity;
        
        this.interactiveObjects.forEach(obj => {
            const distance = cameraPosition.distanceTo(obj.position);
            if (distance < obj.userData.radius && distance < nearestDistance) {
                nearestDistance = distance;
                nearestInteraction = obj;
            }
        });
        
        if (nearestInteraction !== this.currentInteraction) {
            this.currentInteraction = nearestInteraction;
            this.updateInteractionPrompt();
        }
    }

    updateInteractionPrompt() {
        const prompt = document.getElementById('interaction-prompt');
        const icon = document.getElementById('prompt-icon');
        const text = document.getElementById('prompt-text');
        
        if (this.currentInteraction) {
            const data = this.currentInteraction.userData;
            icon.textContent = data.icon;
            text.textContent = `Stiskni E pro ${data.name}`;
            prompt.style.display = 'block';
        } else {
            prompt.style.display = 'none';
        }
    }

    performInteraction() {
        if (this.currentInteraction && window.game) {
            const skill = this.currentInteraction.userData.skill;
            window.game.performAction(skill);
        }
    }

    toggleControls() {
        const controlsHelp = document.getElementById('controls-help');
        controlsHelp.style.display = controlsHelp.style.display === 'block' ? 'none' : 'block';
    }

    toggleInventory() {
        const inventoryPanel = document.getElementById('inventory-panel');
        inventoryPanel.classList.toggle('collapsed');
    }

    updateTime() {
        this.gameTime += this.timeSpeed;
        if (this.gameTime >= 24) {
            this.gameTime = 0;
        }
        
        const hours = Math.floor(this.gameTime);
        const minutes = Math.floor((this.gameTime - hours) * 60);
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        
        document.getElementById('game-time').textContent = timeString;
        
        // Update lighting based on time
        this.updateLighting();
        this.updateWeather();
    }

    updateLighting() {
        const timeRatio = this.gameTime / 24;
        const sunAngle = timeRatio * Math.PI * 2 - Math.PI / 2;
        
        // Update sun position
        this.sunLight.position.x = Math.cos(sunAngle) * 50;
        this.sunLight.position.y = Math.sin(sunAngle) * 50 + 10;
        
        // Update light intensity based on time
        let intensity = Math.max(0.2, Math.sin(sunAngle) * 0.8);
        this.sunLight.intensity = intensity;
        
        // Update sky color
        let skyColor;
        if (this.gameTime < 6 || this.gameTime > 20) {
            skyColor = 0x191970; // Night
        } else if (this.gameTime < 8 || this.gameTime > 18) {
            skyColor = 0xFF6347; // Sunset/sunrise
        } else {
            skyColor = 0x87CEEB; // Day
        }
        
        this.scene.background.setHex(skyColor);
        this.scene.fog.color.setHex(skyColor);
    }

    updateWeather() {
        // Simple weather system
        if (Math.random() < 0.001) { // 0.1% chance per frame to change weather
            const weathers = ['sunny', 'cloudy', 'rainy'];
            this.weather = weathers[Math.floor(Math.random() * weathers.length)];
            
            const weatherIcon = document.getElementById('weather-icon');
            switch (this.weather) {
                case 'sunny':
                    weatherIcon.textContent = '☀️';
                    break;
                case 'cloudy':
                    weatherIcon.textContent = '☁️';
                    break;
                case 'rainy':
                    weatherIcon.textContent = '🌧️';
                    break;
            }
        }
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        this.controls.update();
        this.updateTime();
        
        // Animate water
        this.scene.children.forEach(child => {
            if (child.material && child.material.color && child.material.color.getHex() === 0x006994) {
                child.position.y = -5 + Math.sin(Date.now() * 0.001) * 0.1;
            }
        });
        
        this.renderer.render(this.scene, this.camera);
    }
}

// Global 3D world instance
window.world3D = new World3D();
